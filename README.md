# Sales Dashboard - AI-Powered Sales Management Platform

A comprehensive sales dashboard application with secure authentication, invitation-based registration, and AI-powered data visualization.

## 🚀 Features

### 🔐 Secure Authentication
- **Google Sign-In** and **Username/Password** authentication
- **Firebase Authentication** for user management
- **Invitation-based registration** - only authorized users can register
- **Role-based access control** (Admin/User roles)

### 👋 User Onboarding
- **Guided onboarding flow** for first-time users
- **Profile setup** and preference configuration
- **Dashboard customization** based on user goals

### 📊 Sales Management
- **Complete CRUD operations** for sales records
- **Pipeline management** with customizable stages
- **Deal tracking** with probability and expected close dates
- **Customer and product management**

### 🤖 AI-Powered Analytics
- **Generative AI insights** using Google's Gemini AI
- **Dynamic data visualization** and recommendations
- **Performance analysis** and forecasting
- **Intelligent process optimization** suggestions

### 👨‍💼 Admin Dashboard
- **User management** with role assignment
- **Invitation system** for new user registration
- **Sales performance overview** across teams
- **System administration** tools

### 🔒 Security Features
- **API rate limiting** and request validation
- **Secure middleware** for all endpoints
- **CORS protection** and security headers
- **Input sanitization** and validation

## 🛠️ Tech Stack

- **Frontend**: Next.js 15, React 19, Tailwind CSS
- **Backend**: Next.js API Routes, Firebase Admin SDK
- **Database**: Firebase Firestore
- **Authentication**: Firebase Auth
- **AI**: Google Generative AI (Gemini)
- **UI Components**: Radix UI, Heroicons
- **Styling**: Tailwind CSS with custom animations

## 📋 Prerequisites

- Node.js 18+ and npm
- Firebase project with Firestore and Authentication enabled
- Google AI API key
- Service account key for Firebase Admin SDK

## 🚀 Quick Start

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd sales-dashboard
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   # Edit .env.local with your Firebase and Google AI credentials
   ```

4. **Configure Firebase**
   - Place your service account key at `src/lib/serviceAccountKey.json`
   - Update Firebase configuration in `src/lib/firebase.js`

5. **Run the development server**
   ```bash
   npm run dev
   ```

6. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 📁 Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── DashboardLayout.js
│   ├── ProtectedRoute.js
│   └── ui/             # UI component library
├── contexts/           # React contexts
│   └── AuthContext.js  # Authentication state management
├── lib/               # Utility libraries
│   ├── auth.js        # Authentication helpers
│   ├── dbModels.js    # Database models and operations
│   ├── firebase.js    # Firebase client configuration
│   ├── firebaseAdmin.js # Firebase Admin SDK
│   ├── middleware.js  # API middleware and security
│   └── utils.js       # General utilities
├── pages/             # Next.js pages and API routes
│   ├── api/           # API endpoints
│   │   ├── ai-analytics.js
│   │   ├── invitations.js
│   │   ├── sales.js
│   │   └── admin/
│   ├── admin/         # Admin-only pages
│   ├── analytics.js   # AI-powered analytics
│   ├── dashboard.js   # Main dashboard
│   ├── login.js       # Authentication pages
│   ├── onboarding.js  # User onboarding flow
│   └── sales.js       # Sales management
└── styles/            # Global styles
```

## 🔧 Configuration

### Firebase Setup
1. Create a Firebase project
2. Enable Authentication (Email/Password and Google)
3. Enable Firestore Database
4. Generate service account key
5. Configure security rules (see `deployment.md`)

### Environment Variables
See `.env.example` for all required environment variables:
- Firebase configuration
- Google AI API key
- Security settings
- Rate limiting configuration

## 🧪 Testing

Run the test suite:
```bash
npm test
```

Test coverage includes:
- Authentication flows
- API endpoint security
- Database operations
- Input validation
- Middleware functionality

## 🚀 Deployment

See `deployment.md` for detailed deployment instructions including:
- Vercel deployment (recommended)
- Docker deployment
- Security configuration
- Performance optimization

## 🔐 Security

The application implements multiple security layers:
- **Authentication**: Firebase Auth with token validation
- **Authorization**: Role-based access control
- **API Security**: Rate limiting, CORS, security headers
- **Input Validation**: Comprehensive request validation
- **Database Security**: Firestore security rules

## 📊 AI Features

The AI-powered analytics provide:
- **Performance Analysis**: Automated insights from sales data
- **Forecasting**: Predictive analytics for future performance
- **Process Optimization**: Recommendations for improving sales processes
- **Dynamic Visualizations**: AI-generated charts and graphs

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Check the `deployment.md` for troubleshooting
- Review the test files for usage examples
- Open an issue on GitHub

## 🔄 Version History

- **v1.0.0**: Initial release with core features
  - Secure authentication and authorization
  - Sales management and tracking
  - AI-powered analytics
  - Admin dashboard and user management
