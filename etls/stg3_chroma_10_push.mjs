import sqlite3 from 'sqlite3';
import { open } from 'sqlite';
import axios from 'axios';

import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';
dotenv.config();
import { ChromaClient } from "chromadb";
const client = new ChromaClient();


// Utility function for sleeping
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Fonksiyonlar
function prepareVectorText(object, fields = []) {
  const parts = fields
    .filter(field => Object.prototype.hasOwnProperty.call(object, field))
    .map(field => field + ': ' + normalizeText(object[field] || ' bilgi yok. '));
  const combined = parts.join(', ');
  return normalizeText(combined);
}

function normalizeText(text) {
  if (!text || typeof text !== 'string') return '';
  let cleaned = text;

  // HTML etiketleri ve özel karakterler temizleniyor
  cleaned = cleaned
    .replace(/<[^>]*>/g, ' ')
    .replace(/&nbsp;/gi, ' ')
    .replace(/&amp;/gi, '&')
    .replace(/</gi, '<')
    .replace(/>/gi, '>')
    .replace(/&quot;/gi, '"')
    .replace(/&#39;/gi, "'")
    .replace(/&apos;/gi, "'")
    .replace(/&[a-zA-Z0-9#]+;/g, ' ')
    .replace(/[\t\n\r]/g, ' ')
    .replace(/[\u0000-\u001F\u007F]/g, '')
    .replace(/\s+/g, ' ');

  // Türkçe karakter dönüşümü
  cleaned = cleaned
    .replace(/ç/g, 'c').replace(/Ç/g, 'C')
    .replace(/ğ/g, 'g').replace(/Ğ/g, 'G')
    .replace(/ı/g, 'i').replace(/İ/g, 'I')
    .replace(/ö/g, 'o').replace(/Ö/g, 'O')
    .replace(/ş/g, 's').replace(/Ş/g, 'S')
    .replace(/ü/g, 'u').replace(/Ü/g, 'U');

  return cleaned.toLowerCase();
}

function normalizeJsonObject(jsonObj) {
  const normalizedObj = {};
  for (const key in jsonObj) {
    if (Object.prototype.hasOwnProperty.call(jsonObj, key)) {
      let value = jsonObj[key];
      // Convert null or undefined to empty string before normalization
      if (value === null || value === undefined) {
        value = '';
      }
      normalizedObj[key] = typeof value === 'string' ? normalizeText(value) : value;
    }
  }
  return normalizedObj;
}

async function vectorEmbeddings_ollama({ text }) {
  const uri = 'http://127.0.0.1:11434/api/embeddings';
  const model = "nomic-embed-text:latest";

  if (!text) {
    console.error('ollama: Error no text', text);
    return null;
  }

  try {
    const response = await fetch(uri, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model,
        prompt: text,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('Ollama embedding error:', errorData);
      return null;
    }

    const data = await response.json();
    return data.embedding;
  } catch (error) {
    console.error('Error generating embedding with Ollama:', error);
    return null;
  }
}

const fnChroma = {
  listCollections: async (props = {}) => {
    const { chromaClient = client } = props;
    try {
      let collectionstg = await chromaClient.listCollections();
      let collections = collectionstg.map(coll => coll.name);
      // console.log('Available collections:', collections.map(coll => coll.name));
      return collections;
    } catch (error) {
      // console.error('Error listing collections:', error);
      return [];
    }
  },

  deleteCollection: async (props = {}) => {
    const { collname = 'tours', chromaClient = client } = props;
    try {
      await chromaClient.deleteCollection({ name: collname });
      console.log(`Collection "${collname}" deleted successfully.`);
      return true;
    } catch (error) {
      throw `Error deleting "${collname}" collection. ${error.message}`;
    }
  },
  collectionExists: async ({ chromaClient = client, collname = 'tours' }) => {
    try {
      const collections = await client.listCollections();
      console.log('Available collections:', collections.map(coll => coll.name));
      // Check if the collection with the given name exists
      console.log(`Checking if collection "${collname}" exists...`);
      if (!collections || collections.length === 0) {
        console.log('No collections found.');
        return false; // No collections found
      } else {
        console.log('Collections found.');
      }
      return collections.some(coll => coll.name === collname);
    } catch (error) {
      console.error('Error checking collection existence:', error);
      return false;
    }
  },

  createColl: async ({
    collname = 'noname', dropIfExists = false, chromaClient = client, embedDimension = 768, debug = false
  }) => {
    try {
      //check if collection exists

      let collection;
      const collections = await fnChroma.listCollections({ chromaClient });
      if (collections.includes(collname)) {
        if (dropIfExists) {
          debug && console.log(`Collection "${collname}" already exists. Deleting it...`);
          await fnChroma.deleteCollection({ collname, chromaClient });
        } else {
          debug && console.log(`Collection "${collname}" already exists. Skipping creation.`);
          return true; // Collection already exists, no need to create
        }
      }

      debug && console.log(`Creating collection "${collname}"...`);
      collection = await chromaClient.createCollection({
        name: collname,
        embeddingFunction: null, // Explicitly set to null for compatibility
        metadata: {
          "hnsw:space": "cosine",
          "hnsw:construction_ef": 100,
          "hnsw:search_ef": 100,
          "hnsw:M": 16,
          "dimension": embedDimension // Specify the expected embedding dimension
        }
      });
      debug && console.log(`Collection "${collname}" created successfully..`);
      return true;
    } catch (error) {
      debug && console.error('Error creating collection:', error);
      // return true;
      throw `Error creating "${collname}" collection. ${error.message}`;
    }
  },

  addrec2coll: async (data, collname = 'tours', collection, debug) => {
    try {
      collection = collection || await client.getCollection({ name: collname });
      // debug && console.log(`Attempting to add to ChromaDB:`, JSON.stringify(data, null, 2)); // Log full data including embeddings
      await collection.add(data);
      //   console.log(`Records added to collection "${collname}" successfully..`, data);
      return true;
    } catch (error) {
      debug && console.error('Error adding to collection:', error);
      if (debug) {
        // Do not delete embeddings for now, to see the full problematic data
        console.error('Problematic data (full):', JSON.stringify(data, null, 2));
      }
      return false;
    }
  },

}

// --- ChromaDB Ayarları ---
const COLLECTION_NAME = 'q_summary_vector';

async function createCollectionIfNotExists() {
  try {
    await fnChroma.createColl({ collname: COLLECTION_NAME, dropIfExists: true, debug: true });

  } catch (error) {
    console.error('❌ Koleksiyon oluşturma hatası:', error);
    throw error;
  }
}
async function addEmbeddingToChroma({ id, text, embedding, rawJson, collection, debug = false }) {
  try {
    const batchData = {
      ids: [],
      embeddings: [],
      metadatas: [],
      documents: []
    };
    const metaDataa = { ...rawJson };
    delete metaDataa['rowid']; // rowid'yi kaldır
    delete metaDataa['ACIKLAMA']; // rowid'yi kaldır
    delete metaDataa['KONAKLAMA_ACIKLAMA']; // rowid'yi kaldır

    batchData.ids.push(id);
    batchData.embeddings.push(embedding);
    batchData.metadatas.push({ ...metaDataa });
    batchData.documents.push(text);
    // debug && console.log(`Attempting to add to ChromaDB (from addEmbeddingToChroma):`, JSON.stringify(batchData, null, 2));
    let rec = await fnChroma.addrec2coll(batchData, COLLECTION_NAME, collection, debug);
    if (!rec) {
      debug && console.error(`✗ Eklenemedi (${id}):`);
      return false;
    }
    return true;
  } catch (error) {
    debug && console.error(`✗ Eklenemedi (${id}):`, error.message);
    if (error.response) {
      debug && console.error('ChromaDB Response status:', error.response.status);
      debug && console.error('ChromaDB Response data:', error.response.data);
    }
    return false;
  }
}
// --- Ana İşlem ---
async function queryChromaDB(queryText) {
  const embedding = await vectorEmbeddings_ollama({ text: queryText });

  try {
    const response = await axios.post(`${CHROMA_API_URL}/api/v2/collections/${COLLECTION_NAME}/query`, {
      query_embeddings: [embedding],
      n_results: 5,
      include: ["documents", "metadatas", "distances"]
    });

    console.log(response.data);
    return response.data;
  } catch (error) {
    console.error("❌ Arama hatası:", error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
    return null;
  }
}
async function main() {
  await createCollectionIfNotExists();
  const failedIds = []; // Başarısız olan kayıtların ID'lerini tutacak dizi
  const debug = true; // Hata ayıklama modu
  // SQLite veritabanı yolunu belirle
  const dbPath = path.join(process.cwd(), 'data_hotel_local.db');
  const dbExists = fs.existsSync(dbPath);
  const recordCount = 15000; // İşlenecek kayıt sayısı
  if (dbExists) {
    console.log(`✓ Mevcut SQLite veritabanı bulundu: ${dbPath}`);
  } else {
    // console.log(`✓ Yeni SQLite veritabanı oluşturuluyor: ${dbPath}`);
    console.error(`💥 SQLite veritabanı bulunamadi ${dbPath}`);
    process.exit(1);
  }

  // SQLite veritabanını aç/oluştur
  const db = await open({
    filename: dbPath,
    driver: sqlite3.Database
  });

  const embeddingFields = ['TESIS_ADI', 'KATEGORI', 'BOLGE_ADI', 'ALT_BOLGE_ADI', 'BOLGE_DETAY', 'KIMLER_KALMALI', 'ACIKLAMA', 'KONAKLAMA_ACIKLAMA'];

  try {
    const rows = await db.all(`
          SELECT  rowid, TESIS_ID, TESIS_ADI, KATEGORI, 
                  BOLGE_ADI, ALT_BOLGE_ADI, BOLGE_DETAY, ACIKLAMA, 
                  KONAKLAMA_ACIKLAMA, KIMLER_KALMALI 
          FROM q_summary 
          where WEBSITE_LISTED = 'TRUE'
          --where TESIS_ID IN ( 28,  73, 101, 121, 126, 127, 132, 136 ) 
          limit ${recordCount}
        `
    );
      function isValidEmbedding(embedding, expectedLength = 768) {
      return Array.isArray(embedding) &&
        embedding.length === expectedLength &&
        embedding.every(n => typeof n === 'number' && Number.isFinite(n)); // Check for finite numbers
    }

    // Batch işleme için ayarlar

    const batchBop = Date.now();
    const totalRecords = rows.length;
    const batchSize = totalRecords < recordCount ? totalRecords : recordCount; // Toplam kayıt sayısı veya recordCount, hangisi daha küçükse
    const batchStart = 0;
    const batchEnd = batchStart + batchSize;
    console.log(`📊 Toplam ${totalRecords} kayıt, ${batchSize}'şer batch halinde işlenecek...`);

    let embeddingTimes = [];
    let failedIds = [];
    let rowProcTimes = [];
    let insertTimes = [];
    let collection = await client.getCollection({ name: COLLECTION_NAME, collname: COLLECTION_NAME });
    // Batch'lere böl ve işle
    const currentBatch = rows.slice(batchStart, batchEnd);
    console.log(`📊 Batch ${batchStart + 1} - ${batchEnd} işleniyor...`);
      
    for (const row of currentBatch) {
      try {
        const combinedText = prepareVectorText(row, embeddingFields);
        const embeddingBop = Date.now();
        const embedding = await vectorEmbeddings_ollama({ text: combinedText });

        const isValid = isValidEmbedding(embedding, 768); // 768 boyutlu embedding kontrolü
        if (!isValid) {
          console.log(`✗ Geçersiz embedding (${row.TESIS_ID})`);
          failedIds.push(row.TESIS_ID);
          continue;
        }
        const embeddingEop = Date.now();
        embeddingTimes.push(embeddingEop - embeddingBop);
        if (!embedding) {
          console.log(`✗ Embedding oluşturulamadı (${row.TESIS_ID})`);
          failedIds.push(row.TESIS_ID);
          continue;
        }
        // debug && console.log(`Embedding boyutu (${row.TESIS_ID}): ${embedding?.length}`);
        let rec = await addEmbeddingToChroma({
          id: `hotel_${row.TESIS_ID}`,
          text: combinedText,
          embedding,
          rawJson: normalizeJsonObject(
            row
            // {
            //   tesis_adi: row.TESIS_ADI,
            //   kategori: row.KATEGORI,
            //   bolge_adi: row.BOLGE_ADI,
            //   alt_bolge_adi: row.ALT_BOLGE_ADI,
            //   bolge_detay: row.BOLGE_DETAY,
            //   kimler_kalmasi: row.KIMLER_KALMALI
            // }
          ),
          collection,
          debug: true,
        });
        if (!rec) {
          console.log(`✗ Eklenemedi (${row.TESIS_ID})`);
          failedIds.push(row.TESIS_ID);
          continue;
        }
        const processEop = Date.now();
        rowProcTimes.push(processEop - embeddingBop);
        // console.log(`Eklendi: hotel_${row.TESIS_ID}`);
                // yer 100. kayitta burada ilerleme yüzdesini ekle.
                const currentIndex = currentBatch.indexOf(row);
                if ((currentIndex + 1) % 100 === 0 || (currentIndex + 1) === totalRecords) {
                  const progress = ((currentIndex + 1) / totalRecords * 100).toFixed(2);
                  console.log(`📊 İşleme İlerleme: %${progress} (${currentIndex + 1}/${totalRecords}). Geçen Süre: ${((Date.now() - batchBop) / 1000).toFixed(2)}ms`);
                }

      } catch (error) {
        console.error(`✗ Eklenemedi (${row.TESIS_ID}):`, error.message);
        failedIds.push(row.TESIS_ID);
      }
    }

    const batchEop = Date.now();
    const batchDuration = (batchEop - batchBop) / 1000;
    const avgEmbeddingTime = embeddingTimes.length > 0 ? embeddingTimes.reduce((a, b) => a + b, 0) / embeddingTimes.length / 1000 : 0;
    const avgProcesssTime = rowProcTimes.length > 0 ? rowProcTimes.reduce((a, b) => a + b, 0) / rowProcTimes.length / 1000 : 0;
    console.log(`✅ Batch tamamlandı. Batch Süresi: ${batchDuration.toFixed(2)} s, Ortalama Embedding Süresi: ${avgEmbeddingTime.toFixed(2)} s, Ortalama Process Süresi: ${avgProcesssTime.toFixed(2)} s`);

    console.log(' failedIds:', failedIds.length, failedIds.slice(0, 10));

  } catch (error) {
    console.error("Hata oluştu:", error.message);
  } finally {
    await db.close();
  }
}

main();