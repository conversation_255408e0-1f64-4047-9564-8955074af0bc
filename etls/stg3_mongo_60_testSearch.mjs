import sqlite3 from 'sqlite3';
import { open } from 'sqlite';
import fs from 'fs';
import path from 'path';
import { MongoClient } from 'mongodb';
import dotenv from 'dotenv';
dotenv.config();
import { GoogleGenerativeAI } from '@google/generative-ai';
// import { pipeline, env } from '@xenova/transformers';
// .env dosyasını yükle

// WebAssembly için wasm yollarını ayarlayalım
// env.backends.onnx.wasm.wasmPaths = 'https://onnxruntime.github.io/onnx-js-demo/dist/ ';

const ollamaModel = 'nomic-embed-text:latest';
export const vectorEmbeddings = {
    google: async ({
        text,
        genAIpack,
        model = "text-embedding-004",
    }) => {
        if (!text) {
            console.error('google: Error no text');
            return null
        };
        try {
            // API anahtarı ile Google AI istemcisini başlat
            const GEMINI_API_KEY = process.env.GEMINI_API_KEY_YODA;
            const genAI = genAIpack || new GoogleGenerativeAI(GEMINI_API_KEY);
            // text-embedding-004 modelini al
            const embeddingModel = genAI.getGenerativeModel({ model });
            // Embedding oluştur
            const result = await embeddingModel.embedContent(text);
            // Embedding değerlerini çıkar
            if (result && result.embedding && Array.isArray(result.embedding.values)) {
                return result.embedding.values;
            } else {
                console.error('Unexpected response format from Google AI:', result);
                return null;
            }
        } catch (error) {
            console.error('Error generating embedding with Google AI:', error);
            return null;
        }
    },
    ollama: async ({
        text,
        uri = 'http://127.0.0.1:11434/api/embeddings',
        model = ollamaModel || "mxbai-embed-large:latest",
    }) => {
        if (!text) {
            console.error('ollama: Error no text', text);
            return null
        };

        try {
            const response = await fetch(uri, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    model, prompt: text,
                }),
            });

            if (!response.ok) {
                const errorData = await response.json();
                console.error('Ollama embedding error:', errorData);
                return null;
            }

            const data = await response.json();
            return data.embedding;
        } catch (error) {
            console.error('Error generating embedding with Ollama:', error);
            return null;
        }
    },
    bertTurkish: async ({ text }) => {
        if (!text) {
            console.error('bertTurkish: Error no text', text);
            return null;
        }

        try {
            const response = await fetch('http://127.0.0.1:8080/embed', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ text }),
            });
            if (!response.ok) {
                const errorData = await response.json();
                console.error('BERT Turkish Service Error:', errorData);
                return null;
            }

            const data = await response.json();
            return data.embedding; // Float32Array değilse zaten array gelir
        } catch (error) {
            console.error('Error generating Turkish BERT embedding:', error);
            return null;
        }
    },
    // bertTurkishx: async ({ text }) => {
    //     if (!text) {
    //         console.error('bertTurkish: Error no text', text);
    //         return null;
    //     }

    //     try {
    //         // Model zaten cached olabilir; tekrar yüklememek için singleton yapısı da kullanılabilir
    //         const extractor = await pipeline('feature-extraction', 'dbmdz/bert-base-turkish-cased');

    //         // Embedding çıkarma işlemi (normalize ve mean pooling ile)
    //         const output = await extractor(text, {
    //             pooling: 'mean',
    //             normalize: true
    //         });

    //         // Float32Array -> Array<number> çevir
    //         return Array.from(output.data);
    //     } catch (error) {
    //         console.error('Error generating Turkish BERT embedding:', error);
    //         return null;
    //     }
    // }
}

function normalizeText(text) {
    if (!text || typeof text !== 'string') return '';
    // HTML kod bloklarını kaldır
    let cleaned = text; // text.replace(/<[^>]*>/g, ' ');
    // HTML escape karakterlerini karşılıklarıyla değiştir
    cleaned = cleaned
        .replace(/&nbsp;/gi, ' ')
        .replace(/&amp;/gi, '&')
        .replace(/&lt;/gi, '<')
        .replace(/&gt;/gi, '>')
        .replace(/&quot;/gi, '"')
        .replace(/&#39;/gi, "'")
        .replace(/&apos;/gi, "'")
        // Diğer tüm &xxxx; biçimindekileri boşlukla değiştir
        .replace(/&[a-zA-Z0-9#]+;/g, ' ');

    // HTML kod bloklarını kaldır
    cleaned = cleaned.replace(/<[^>]*>/g, ' ');
    // \t, \n, \r ve kontrol karakterlerini temizle (0x00-0x1F ve 0x7F)
    cleaned = cleaned.replace(/[\t\n\r]/g, ' ');
    cleaned = cleaned.replace(/[\u0000-\u001F\u007F]/g, '');
    // Fazla boşlukları tek boşluğa indir
    cleaned = cleaned.replace(/\s+/g, ' ');
    // Önce Türkçe karakterleri eşlenik İngilizce karakterlerle değiştir
    cleaned = cleaned
        .replace(/ç/g, 'c')
        .replace(/Ç/g, 'C')
        .replace(/ğ/g, 'g')
        .replace(/Ğ/g, 'G')
        .replace(/ı/g, 'i')
        .replace(/İ/g, 'I')
        .replace(/ö/g, 'o')
        .replace(/Ö/g, 'O')
        .replace(/ş/g, 's')
        .replace(/Ş/g, 'S')
        .replace(/ü/g, 'u')
        .replace(/Ü/g, 'U');
    // Sonra lowercase dönüşümü
    cleaned = cleaned.toLowerCase();
    return cleaned.trim();
}

function prepareVectorText(object, fields = []) {
    // Seçili alanları normalize edip birleştir
    const parts = fields
        .filter(field => Object.prototype.hasOwnProperty.call(object, field))
        .map(field => normalizeText(object[field] || ''));
    const combined = parts.join(' ');
    return normalizeText(combined);
}

async function main() {
    const dtBop = Date.now();

    try {
        const tables2Sync = [
            {
                mongoCollection: 'tourai.data.dim.hotelsdomestic',
                needsVectorEmbedding: true,
                embeddingFields: ['TESIS_ADI', 'KATEGORI', 'BOLGE_ADI', 'ALT_BOLGE_ADI', 'BOLGE_DETAY', 'ACIKLAMA', 'KONAKLAMA_ACIKLAMA']
            },
            // {
            //     mongoCollection: 'tourai.data.dim.hotelsdomestic_summary',
            //     needsVectorEmbedding: false,
            //     embeddingFields: ['TESIS_ADI', 'KATEGORI', 'BOLGE_ADI', 'ALT_BOLGE_ADI', 'BOLGE_DETAY', 'ACIKLAMA', 'KONAKLAMA_ACIKLAMA']
            // },
        ];

        // MongoDB bağlantısı
        const mongoUri = process.env.MONGODB_TOUR_URI;
        if (!mongoUri) {
            console.error('💥 MONGODB_TOUR_URI environment variable bulunamadı!');
            process.exit(1);
        }

        console.log('🔗 MongoDB\'ye bağlanılıyor...');
        const mongoClient = new MongoClient(mongoUri);
        await mongoClient.connect();
        console.log('✅ MongoDB bağlantısı başarılı');

        const mongoDb = mongoClient.db('toursdb');
        const collection = mongoDb.collection('tourai.data.dim.hotelsdomestic');
        // // Komut satırı argümanlarını al
        const arg = process.argv.slice(2);
        let query;
        if (arg && arg.length > 0 && typeof arg[0] === 'string' && arg[0].trim().length > 0) {
            query = arg[0];
        } else {
            query = 'tenis kort ışıklandırması olan kemer de bir otel';
        }

        // Metni normalize et
        const normalizedQuery = normalizeText(query);

        // Embedding değerini al (ör: Google ile)
        const embedding = await vectorEmbeddings.bertTurkish({ text: normalizedQuery });

        if (!embedding) {
            console.error('💥 Embedding alınamadı!');
            process.exit(1);
        }
        console.log('query:', query);
        console.log('normalizedQuery:', normalizedQuery);
        console.log('embedding length:', embedding.length);
        // Vector Search ile sorgula (Atlas Vector Search örneği)
        const pipeline = [
            {
                $vectorSearch: {
                    index: "hotelsdomestic", // index adını güncelle
                    path: "vectorEmbedding", // koleksiyondaki embedding alanı
                    queryVector: embedding,
                    numCandidates: 100,
                    limit: 10,
                }
            },
            {
                $project: {
                    _id: 0,
                    TESIS_ADI: 1,
                    KATEGORI: 1,
                    BOLGE_ADI: 1,
                    ALT_BOLGE_ADI: 1,
                    BOLGE_DETAY: 1,
                    ACIKLAMA: 1,
                    KONAKLAMA_ACIKLAMA: 1,
                    score: { $meta: "vectorSearchScore" }
                }
            }
        ];

        const results = await collection.aggregate(pipeline).toArray();
        console.log('Sonuçlar:', results);

        // Sonuçları dosyaya yaz
        fs.writeFileSync(
            path.join(process.cwd(), 'stg3_mongo_60_results.json'),
            JSON.stringify(results, null, 2),
            'utf-8'
        );
        await mongoClient.close();
        process.exit(0);
    } catch (error) {
        console.error('💥 Genel hata oluştu:', error);
        process.exit(1);
    }
}

// Script'i çalıştır
main();
