import chromadb

# Initialize a ChromaDB client (using a persistent client for demonstration)
# You might need to adjust the path based on your ChromaDB setup
client = chromadb.HttpClient(host="localhost", port=8000)

# List all collections
collections = client.list_collections()

print("ChromaDB Collections:", collections)
if collections:
    print("\nDetails for 'q_summary_vector' collection:")
    try:
        collection = client.get_collection(name="q_summary_vector")
        # Fetch all items from the collection
        # You can specify 'limit' and 'offset' for pagination if the collection is large
        # You can also specify 'where', 'where_document', 'query_embeddings', 'n_results' for filtering
        results = collection.get(
        )
        
        if results['ids']:
            for i in range(len(results['ids'])):
                print(f"  ID: {results['ids'][i]}")
                if results['documents'] and results['documents'][i]:
                    print(f"  Document: {results['documents'][i][:100]}...") # Print first 100 chars
                if results['metadatas'] and results['metadatas'][i]:
                    print(f"  Metadata: {results['metadatas'][i]}")
                print("-" * 20)
        else:
            print("  No records found in 'q_summary_vector' collection.")
    except Exception as e:
        print(f"  Error accessing 'q_summary_vector' collection: {e}")
else:
    print("No collections found in ChromaDB.")