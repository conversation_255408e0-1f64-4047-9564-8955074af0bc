from fastapi import FastAPI, Body
from pydantic import BaseModel
from sentence_transformers import SentenceTransformer
import uvicorn
import numpy as np

app = FastAPI()

# Model ilk başlatmada yüklenir
model = SentenceTransformer('dbmdz/bert-base-turkish-cased')

class EmbedRequest(BaseModel):
    text: str

@app.get("/embed")
async def get_embedding_get(text: str):
    if not text:
        return {"error": "No text provided"}
    try:
        embedding = model.encode([text])[0].tolist()
        return {"embedding": embedding}
    except Exception as e:
        return {"error": str(e)}

@app.post("/embed")
async def get_embedding_post(body: EmbedRequest):
    text = body.text
    if not text:
        return {"error": "No text provided"}
    try:
        embedding = model.encode([text])[0].tolist()
        return {"embedding": embedding}
    except Exception as e:
        return {"error": str(e)}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8080)