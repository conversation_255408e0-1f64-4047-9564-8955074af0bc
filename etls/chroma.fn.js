const { ChromaClient } = require('chromadb');
const tours = require('../repo/toursraw.json');

const client = new ChromaClient();
const collName = 'tours';

async function initializeCollection(collname = 'tours') {
  try {
    // "tours" koleksiyonunu oluştur
    console.log(`creating Collection "${collname}"..`);
    const collection = await client.createCollection({ name: collname });
    console.log(`Collection "${collname}" created successfully..`);
    return true;
  } catch (error) {
    console.error('Error creating collection:', error);
    return false
  }
}
async function checkCollectionExists(collname = 'tours', createIF = true, dropCreateIF = false) {
  try {
    // Mevcut koleksiyonları listele
    let collections = [];

    try {
      collections = await client.listCollections();
      console.log('Existing collections:', collections);
    } catch (eC) { }

    // "tours" koleksiyonunu kontrol et
    const toursCollection = collections.find(col => col === collname);
    if (toursCollection) {
      console.log(`Collection "${collname}" exists.`);
      if (!dropCreateIF) {
        return true;
      } else {
        await client.deleteCollection({ name: collName });
        console.log(`Collection "${collname}" deleted.`);
        let cCol = await initializeCollection(collName);
        return cCol;
      }
    } else {
      console.log(`Collection "${collname}" does not exists.`);
      if (!createIF) {
        return false
      } else {
        let cCol = await initializeCollection(collName);
        return cCol;
      }
    }
  } catch (error) {
    console.error('Error checking collections:', error);
    return false
  }
}

// Add this helper function at the top level
function normalizeText(text) {
  if (!text) return '';
  return text.toLowerCase()
    .replace(/ç/g, 'c')
    .replace(/ğ/g, 'g')
    .replace(/ı/g, 'i')
    .replace(/ö/g, 'o')
    .replace(/ş/g, 's')
    .replace(/ü/g, 'u')
    .replace(/[^a-z0-9\s]/g, ' ')
    .split(/\s+/)
    .filter(Boolean)
    .join(' ')
    .trim();
}

//data imports -- refresh knowledgebase...
async function initializeChromaDB() {
  try {
    if (await checkCollectionExists(collName, true, true)) {
      console.log('Process Imports!');
      const collection = await client.getCollection({ name: collName });
      
      // Process tours in batches
      const batchSize = 10;
      for (let i = 0; i < tours.tours.length; i += batchSize) {
        const batch = tours.tours.slice(i, i + batchSize);
        const batchData = {
          ids: [],
          embeddings: [],
          metadatas: [],
          documents: []
        };
        
        for (const tour of batch) {
          // Normalize text fields for better matching
          const normalizedFields = {
            title: normalizeText(tour.TUR_ADI),
            details: normalizeText(tour.DETAY),
            program: normalizeText(tour.TUR_PROGRAM),
            notes: normalizeText(tour.KATILIM_NOTU),
            cities: normalizeText(tour.SEHIRLER),
            startCity: normalizeText(tour.BASLANGIC_SEHIR_ADI),
            startCountry: normalizeText(tour.BASLANGIC_ULKE_ADI),
            tourType: normalizeText(tour.TUR_TIP),
            subType: normalizeText(tour.TUR_ALTTIP)
          };
          
          // Create searchable document text
          const docData = Object.values(normalizedFields).join(' ');
          
          const embedding = await generateEmbedding(docData);
          batchData.ids.push(tour.TUR_ID.toString());
          batchData.embeddings.push(embedding);
          batchData.metadatas.push({
            ...normalizedFields,
            originalTitle: tour.TUR_ADI || '',
            originalLocation: tour.SEHIRLER || '',
            startDate: tour.ILK_DONEM_TARIHI || '',
            endDate: tour.SON_DONEM_TARIHI || '',
            price: tour.MIN_FIYAT || '',
            currency: tour.PARA_BIRIMI || '',
            duration: tour.TUR_GUN || '',
            requiresVisa: tour.VIZE_GEREK === 1 ? 'Yes' : 'No'
          });
          batchData.documents.push(docData);
        }
        
        if (batchData.ids.length > 0) {
          await collection.add(batchData);
          console.log(`Processed batch #${i/batchSize + 1}`);
        }
      }
    }
    console.log('ChromaDB initialized successfully');
  } catch (error) {
    console.error('Fatal error initializing ChromaDB:', error);
    throw error;
  }
}
async function queryChromaDBwIntent(queryTextStg, intentData) {
  try {
    // Koleksiyon bağlantısını al
    const collection = await client.getCollection({ name: collName });

    // Sorgu metnini ön işleme tabi tut
    const queryText = normalizeText(queryTextStg);
    console.log("Query text:", queryTextStg, queryText);

    // Sorgu embedding'ini oluştur
    const queryEmbedding = await generateEmbedding(queryText);
    console.log("Query embedding:", [...queryEmbedding].slice(-3));

    // Sorgu seçeneklerini oluştur
    const queryOptions = {
      queryEmbeddings: [queryEmbedding],
      nResults: 5,
      include: ['metadatas', 'documents']
    };

    // Intent verilerine göre filtre ekle
    if (intentData?.details) {
      const whereClause = {};
      if (intentData.details.location) {
        whereClause.location = { $contains: intentData.details.location };
      }
      if (intentData.details.startDate) {
        whereClause.startDate = { $gte: intentData.details.startDate };
      }
      if (intentData.details.endDate) {
        whereClause.endDate = { $lte: intentData.details.endDate };
      }
      if (intentData.details.minPrice !== undefined) {
        whereClause.price = { $gte: intentData.details.minPrice };
      }
      if (intentData.details.maxPrice !== undefined) {
        whereClause.price = { ...whereClause.price, $lte: intentData.details.maxPrice };
      }
      if (Object.keys(whereClause).length > 0) {
        queryOptions.where = whereClause;
      }
    }

    // Sorguyu çalıştır
    const queryResults = await collection.query(queryOptions);

    // Sonuçları kontrol et
    if (queryResults.ids && queryResults.ids.length > 0) {
      console.log("Found the following IDs:", queryResults.ids);
      // console.log("Documents:", queryResults.documents);
      // console.log("Metadatas:", JSON.stringify(queryResults.metadatas));
      return queryResults;
    } else {
      console.log("No matching documents found.");
      return false;
    }
  } catch (error) {
    console.error("Error querying ChromaDB:", error);
    throw error;
  }
}

async function generateEmbedding(text) {
  try {
    const response = await fetch('http://127.0.0.1:11434/api/embed', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: process.env.EMBED_MODEL || 'mxbai-embed-large:latest', // || 'nomic-embed-text:latest',
        input: text,
      }),
    });

    if (!response.ok) {
      const errorDetails = await response.json();
      throw new Error(`HTTP error! Status: ${response.status}, Details: ${JSON.stringify(errorDetails)}`);
    }

    const data = await response.json();

    // Ensure the embedding is a flat array
    let embedding = data.embeddings;

    // Flatten the embedding if it's nested
    if (Array.isArray(embedding[0])) {
      embedding = embedding.flat();
    }

    // Log the embedding for debugging
    // console.log('Generated Embedding:', embedding.slice(0, 3));

    return embedding;
  } catch (error) {
    console.error('Error generating embedding:', error);
    throw error;
  }
}
async function getCollectionData(collname = 'tours', returnMode = 'count') {
  try {
    if (await checkCollectionExists(collName, false, false)) {
       
      const collection = await client.getCollection({ name: collName });
      const data = await collection.get();
      const count = await collection.count();
      const resp = returnMode == 'count' ? count : data.slice( -5 );
      console.log('getCollectionData', resp);
      return resp
      // Process tours in batches
      
    } else {
      console.log('ChromaDB tasd not found');
      return (false)
    }
    // 
    
  } catch (error) {
    console.error('Fatal error initializing ChromaDB:', error);
    throw error;
  }
}


async function syncMongoToChroma(dropCreate = false, res, q) {
    // 1. MongoDB bağlantısı
    // const client = new MongoClient(process.env.MONGO_URI || 'mongodb://localhost:27017');
    // const db = client.db('toursdb');
    // const collection = db.collection('tours');
    let dtBop = Date.now();
    let localClient = await localClientPromise;
    const localDb = localClient.db('toursdb');
    const toursCollection = localDb.collection('tours');
    const totalTours = await toursCollection.countDocuments(); //20; // 
    console.log(`Found ${totalTours} tours to process`);

    let embeddingFunction = vectorEmbeddings.ollama;
    let embedDimension = 1024;

    // 2. ChromaDB bağlantısı
    // const chromaClient = new ChromaClient();
    const collname = 'tours';

    try {
        // // Check and delete existing collection with retry
        let collection;
        // First get existing collections
        const collections = await chromaClient.listCollections();
        const existingCollection = collections.includes(collname);
        console.log('Chroma Collections', collections, collname, existingCollection)
        if(existingCollection) {
            collection = await chromaClient.getCollection({ name: collname });
            if (dropCreate) {
                try {
                    await chromaClient.deleteCollection({ name: collname });
                    console.log(`Deleted existing collection "${collname}"`);
                } catch (error) {
                    console.log(`No existing collection to delete: ${error.message}`);
                }
            }
        } else {
            collection = await chromaClient.createCollection({
                name: collname,
                embeddingFunction: null, // Explicitly set to null for compatibility
                metadata: {
                    "hnsw:space": "cosine",
                    "hnsw:construction_ef": 100,
                    "hnsw:search_ef": 100,
                    "hnsw:M": 16,
                    "dimension": embedDimension // Specify the expected embedding dimension
                }
            });
            // Process in batches of 50 to avoid memory issues
            const batchSize = 200;
            let processedCount = 0;
            let batchNumber = 1;
            let projection = { embedding: 0, embedding_768d: 0, embedding_1024d: 0, TUR_FIYATLAR: 0, TUR_TARIHLER: 0, }

            // Process all tours in batches
            while (processedCount < totalTours) {
                const tours = await toursCollection.find({})
                    .project(projection)
                    .skip(processedCount)
                    .limit(batchSize)
                    .toArray();

                if (tours.length === 0) break;
                await processChromaBatch({
                    tours,
                    collection,
                    batchNumber,
                    batchSize,
                    embeddingFunction,
                    embedDimension,
                }
                );

                processedCount += tours.length;
                batchNumber++;
                console.log(`Progress: ${processedCount}/${totalTours} tours processed (${Math.round(processedCount / totalTours * 100)}%)`);
            }
            console.log('🎉 Sync completed successfully.');
        }

        try {
            // Get the dimensions from the first embedding
            const embedArr = await vectorEmbeddings.ollama({text: q || 'tur'});
            collection = await chromaClient.getCollection({ name: collname });

            const sampleTour = await collection.query({
                queryEmbeddings: [embedArr],
                nResults: 2,
                include: ['metadatas', 'documents']
            });

            return res.status(200).json({
                success: true,
                elapsed: Date.now() - dtBop,
                data: { sampleTour, }
            });
        }
        catch (e) {
            console.log('Vector search index get failed', e);
            return res.status(501).json({
                success: false,
            });
        }

        // await client.close();
    } catch (error) {
        console.error('ChromaDB Error:', error);
        throw error;
    }
}
const fnPost = {
    ragdbupdate: (req, res) => {
        return new Promise(async (resolve, reject) => {
            const { full = 1 } = req.body;
            try {
                await initializeChromaDB();
                resolve(true)
            } catch (error) {
                console.error(error);
                reject({ error: 'An error occurred while processing your request.' });
            }
        })
    },

    ragdbsampledata: (req, res) => {
        return new Promise(async (resolve, reject) => {
            const { full = 1 } = req.body;
            try {
                let rec = await getCollectionData();
                resolve(rec)
            } catch (error) {
                console.error(error);
                reject({ error: 'An error occurred while processing your request.' });
            }
        })
    },
    ragdbtestdata: (req, res) => {
        return new Promise(async (resolve, reject) => {
            const { prompt = 'Riga Sehir Turu var mı', intent = 'detay_talebi', details = { locations: [ 'Riga' ], activities: [ 'Şehir Turu' ] }  } = req.body;
            try {
            let intentData = {intent, details};
                
            console.log('Message intent:', intentData, prompt);
            queryChromaDBwIntent(prompt, intentData)
                .then((results) => {
                    if (results) {
                        console.log("Matching tours:", results);
                        resolve(results)
                    } else {
                        console.log("No tours found.");
                        resolve([])
                    }
                })
                .catch((error) => {
                    console.error("Error:", error);
                    resolve([])
                });
                
            } catch (error) {
                console.error(error);
                resolve([])
                // reject({ error: 'An error occurred while processing your request.' });
            }
        })
    },

    comparetours: (req, res) => {
        return new Promise(async (resolve, reject) => {
            try {
                const dt = Date.now();
                let rBody;
                let aiEnabled = true;
                let aiSource = 'Gemini';
                let fnAI = aiSource == 'Gemini' ? GetGemini : GetRouter
                try {
                    rBody = typeof req.body !== 'object' ? JSON.parse(req.body) : req.body;
                } catch (e) {
                    console.log('error1', e.message);
                }
                // console.log('req body', rBody)
                if (rBody) {

                    let qPre = fnPrompts.comparetours({ payload: rBody })
                    let qX = await fnAI({ modelName: aiSource == 'Gemini' ? "gemini-2.0-flash-lite" : "qwen/qwen-max", qPrompt: qPre, });
                    console.log('prompt', qPre);
                    console.log('resp', JSON.stringify(qX));

                    resolve(qX)
                } else {
                    reject('no role info')
                }
            } catch (e) {
                console.log('ex', e?.message)
                reject(e.message)
            }
        });
    },

    listroutes: (req, res) => {
        return new Promise(async (resolve, reject) => {
            try {
                let rBody;
                let aiEnabled = true;
                let aiSimulator = false;
                let aiSource = 'Gemini';
                let fnAI = aiSource == 'Gemini' ? GetGemini : GetRouter
                try {
                    rBody = typeof req.body !== 'object' ? JSON.parse(req.body) : req.body;
                } catch (e) {
                    console.log('error1', e.message);
                }
                // console.log('req body', rBody)
                if (rBody) {
                    let qPre = fnPrompts.generateRouteList({ payload: rBody });
                    let qX = aiEnabled ? aiSimulator ? result_Simulator : await fnAI({ modelName: aiSource == 'Gemini' ? "gemini-2.0-flash-lite" : "qwen/qwen-max", qPrompt: qPre, }) : qPre;
                    // console.log('prompt', qPre);
                    // console.log('resp', JSON.stringify(qX));
                    resolve(qX)
                } else {
                    reject('no route info')
                }
            } catch (e) {
                console.log('elist', e?.message)
                reject(e.message)
            }
        });
    },

    comparesimulation: (req, res) => {
        let resp = {
            "result": {
                "comparison_title": "Tur Paketleri Karşılaştırmalı Analizi",
                "basic_info": {
                    "tour1": {
                        "tour_title": "Otobüs İle Büyük İtalya - Yunanistan - Balkan Turu",
                        "agency": "Viya Travel",
                        "duration": "9 Gün",
                        "price_range": "19719 TRY - 21695 TRY (499 EUR - 549 EUR)",
                        "transportation": "Lüks Otobüsler"
                    },
                    "tour2": {
                        "tour_title": "Otobüs İle Selanik - Halkidiki - Thassos Turu",
                        "agency": "Mayak Tour",
                        "duration": "4 Gün",
                        "price_range": "5097 TRY (129 EUR)",
                        "transportation": "Lüks Otobüsler"
                    },
                    "comparison": "Değerli müşterimiz, Viya Travel'ın 9 günlük İtalya, Yunanistan ve Balkanlar turu geniş bir coğrafyayı kapsarken, Mayak Tour'un 4 günlük Selanik, Halkidiki ve Thassos turu daha kısa ve odaklı bir Yunanistan deneyimi sunuyor. İlk tur daha kapsamlı bir Avrupa turu arayanlara, ikinci tur ise Yunanistan'ın belirli bölgelerini keşfetmek isteyenlere hitap ediyor. Her iki tur da lüks otobüslerle ulaşım sağlıyor."
                },
                "destinations": {
                    "tour1": {
                        "countries": "Bulgaristan, Sırbistan, İtalya, Yunanistan",
                        "cities": "Sofya, Belgrad, Ljubljana, Verona, Milano, Venedik, Floransa, Pisa, Siena, Roma, Igoumenitsa, Selanik"
                    },
                    "tour2": {
                        "countries": "Yunanistan",
                        "cities": "İpsala, Thassos Adası, Selanik, Halkidiki, Kavala"
                    },
                    "comparison": "İlk tur, 12 şehir ve 4 ülkeyi kapsayan zengin bir kültürel deneyim sunarken, ikinci tur 5 şehir ve yalnızca Yunanistan'ı içeriyor. Eğer birden fazla ülkeyi ve büyük şehirleri görmek istiyorsanız ilk tur sizin için daha uygun olabilir. Ancak, Yunanistan'ın sahil şeridini ve adalarını keşfetmek istiyorsanız ikinci tur daha ideal bir seçenek."
                },
                "price_value": {
                    "tour1": {
                        "included": "Mesleki Sorumluluk Sigortası, Ekonomi Sınıfı Uçuşlar, Rehberlik Hizmetleri, Gümrük Vergisi, Feribot Bileti, Belirli Turlar, eSIM (1GB, Balkanlar hariç), 6 Gece Oda Kahvaltı Konaklama",
                        "excluded": "Kişisel Harcamalar, Ekstra Turlar, Müze Giriş Ücretleri, Öğle/Akşam Yemekleri, İçecekler, Bahşişler, Şehir Vergileri, Vize Ücreti, Seyahat Sağlık Sigortası"
                    },
                    "tour2": {
                        "included": "Mesleki Sorumluluk Sigortası, Rehberlik Hizmetleri, Lüks Otobüslerle Ulaşım, Belirtilen Kategorideki Otellerde 2 Gece Oda & Kahvaltı Konaklama, eSIM (1GB, Balkanlar hariç)",
                        "excluded": "Kişisel Harcamalar, Ekstra Turlar, Öğle/Akşam Yemekleri, Müze Giriş Ücretleri, Bahşişler, Vize Ücreti, Seyahat Sağlık Sigortası"
                    },
                    "comparison": "İlk tur daha geniş bir coğrafyayı kapsadığı için daha yüksek bir fiyata sahip. Fiyata dahil olan hizmetler arasında uçuşlar ve bazı turlar bulunuyor. İkinci tur daha uygun fiyatlı olsa da, ekstra turlar ve yemekler için ek bütçe ayırmanız gerekebilir. Her iki turda da vize ücreti ve seyahat sağlık sigortası fiyata dahil değil."
                },
                "tour_highlights": {
                    "tour1": {
                        "highlights": "İtalya'nın tarihi şehirleri, Yunanistan'ın antik kalıntıları ve Balkanların kültürel zenginlikleri. Roma, Floransa, Venedik gibi ikonik şehirleri ziyaret etme fırsatı.",
                        "tags": "Akdeniz, Alışveriş, Avrupa, Balayı, Balkan, Bulgar, Bulgaristan, Eğlence, Gece Hayatı, Gezi, Gurme, İtalya, Kültür, Moda, Müze, Romantik, Rumeli, Sanat, Sırbistan, Sırp, Tarih, Yeme - İçme, Yunan, Yunanistan, Yürüyüş"
                    },
                    "tour2": {
                        "highlights": "Yunanistan'ın en güzel adalarından Thassos'ta yüzme molası, Selanik'in tarihi ve kültürel mirası, Halkidiki'nin muhteşem plajları.",
                        "tags": "Ada, Adalar, Avrupa, Balayı, Balkan, Deniz, Eğlence, Gece Hayatı, Gezi, Güneş, Kum, Kültür, Romantik, Tarih, Yaz Tatili, Yeme - İçme, Yunan, Yunanistan"
                    },
                    "comparison": "İlk tur, Avrupa'nın kültürel ve tarihi zenginliklerini keşfetmek isteyenler için ideal. İkinci tur ise Yunanistan'ın deniz, kum ve güneşinin tadını çıkarmak isteyenler için daha cazip. Her iki tur da yerel lezzetleri tatma ve eğlence fırsatları sunuyor."
                },
                "ideal_for": {
                    "tour1": "Avrupa'yı kapsamlı bir şekilde gezmek isteyen, kültürel ve tarihi mekanlara ilgi duyan, uzun süreli bir tatil planlayan gezginler.",
                    "tour2": "Yunanistan'ın belirli bölgelerini keşfetmek isteyen, deniz tatili ve güneşlenmeyi seven, kısa süreli bir kaçamak arayan gezginler."
                },
                "pros_and_cons": {
                    "tour1": {
                        "pros": "Geniş bir coğrafyayı kapsıyor, birçok farklı kültürü deneyimleme fırsatı sunuyor, önemli tarihi ve turistik yerleri içeriyor.",
                        "cons": "Daha yüksek maliyetli, daha fazla seyahat süresi gerektiriyor, bazı öğünler ve aktiviteler fiyata dahil değil."
                    },
                    "tour2": {
                        "pros": "Daha uygun fiyatlı, kısa süreli bir tatil için ideal, Yunanistan'ın sahil şeridini ve adalarını keşfetme fırsatı sunuyor.",
                        "cons": "Daha az sayıda şehir ve ülke içeriyor, ekstra turlar ve yemekler için ek bütçe gerektiriyor."
                    }
                },
                "final_verdict": {
                    "recommendation": "Eğer Avrupa'yı kapsamlı bir şekilde keşfetmek ve farklı kültürleri deneyimlemek istiyorsanız, Viya Travel'ın 'Otobüs İle Büyük İtalya - Yunanistan - Balkan Turu' sizin için daha uygun olabilir. Ancak, Yunanistan'ın sahil şeridinde keyifli bir tatil geçirmek ve bütçenizi daha iyi kontrol etmek istiyorsanız, Mayak Tour'un 'Otobüs İle Selanik - Halkidiki - Thassos Turu'nu tercih edebilirsiniz. Kararınızı verirken, ilgi alanlarınızı, bütçenizi ve tatil sürenizi göz önünde bulundurmanızı öneririz."
                }
            },
            "modelNameAct": "gemini-2.0-flash",
            "source": "geminiAI",
            "sure": 9612,
            "posted_text": null
        }

        return new Promise(async (resolve, reject) => {
            await sleep(3000);
            resolve(resp)
        });
    },

    compareserversimulation: (req, res) => {
        let resp = {
            "result": {
                "comparison_title": "Tur Paketleri Karşılaştırmalı Analizi",
                "basic_info": {
                    "tour1": {
                        "tour_title": "Otobüs İle Büyük İtalya - Yunanistan - Balkan Turu",
                        "agency": "Viya Travel",
                        "duration": "9 Gün",
                        "price_range": "19719 TRY - 21695 TRY (499 EUR - 549 EUR)",
                        "transportation": "Lüks Otobüsler"
                    },
                    "tour2": {
                        "tour_title": "Otobüs İle Selanik - Halkidiki - Thassos Turu",
                        "agency": "Mayak Tour",
                        "duration": "4 Gün",
                        "price_range": "5097 TRY (129 EUR)",
                        "transportation": "Lüks Otobüsler"
                    },
                    "comparison": "Değerli müşterimiz, Viya Travel'ın 9 günlük İtalya, Yunanistan ve Balkanlar turu geniş bir coğrafyayı kapsarken, Mayak Tour'un 4 günlük Selanik, Halkidiki ve Thassos turu daha kısa ve odaklı bir Yunanistan deneyimi sunuyor. İlk tur daha kapsamlı bir Avrupa turu arayanlara, ikinci tur ise Yunanistan'ın belirli bölgelerini keşfetmek isteyenlere hitap ediyor. Her iki tur da lüks otobüslerle ulaşım sağlıyor."
                },
                "destinations": {
                    "tour1": {
                        "countries": "Bulgaristan, Sırbistan, İtalya, Yunanistan",
                        "cities": "Sofya, Belgrad, Ljubljana, Verona, Milano, Venedik, Floransa, Pisa, Siena, Roma, Igoumenitsa, Selanik"
                    },
                    "tour2": {
                        "countries": "Yunanistan",
                        "cities": "İpsala, Thassos Adası, Selanik, Halkidiki, Kavala"
                    },
                    "comparison": "İlk tur, 12 şehir ve 4 ülkeyi kapsayan zengin bir kültürel deneyim sunarken, ikinci tur 5 şehir ve yalnızca Yunanistan'ı içeriyor. Eğer birden fazla ülkeyi ve büyük şehirleri görmek istiyorsanız ilk tur sizin için daha uygun olabilir. Ancak, Yunanistan'ın sahil şeridini ve adalarını keşfetmek istiyorsanız ikinci tur daha ideal bir seçenek."
                },
                "price_value": {
                    "tour1": {
                        "included": "Mesleki Sorumluluk Sigortası, Ekonomi Sınıfı Uçuşlar, Rehberlik Hizmetleri, Gümrük Vergisi, Feribot Bileti, Belirli Turlar, eSIM (1GB, Balkanlar hariç), 6 Gece Oda Kahvaltı Konaklama",
                        "excluded": "Kişisel Harcamalar, Ekstra Turlar, Müze Giriş Ücretleri, Öğle/Akşam Yemekleri, İçecekler, Bahşişler, Şehir Vergileri, Vize Ücreti, Seyahat Sağlık Sigortası"
                    },
                    "tour2": {
                        "included": "Mesleki Sorumluluk Sigortası, Rehberlik Hizmetleri, Lüks Otobüslerle Ulaşım, Belirtilen Kategorideki Otellerde 2 Gece Oda & Kahvaltı Konaklama, eSIM (1GB, Balkanlar hariç)",
                        "excluded": "Kişisel Harcamalar, Ekstra Turlar, Öğle/Akşam Yemekleri, Müze Giriş Ücretleri, Bahşişler, Vize Ücreti, Seyahat Sağlık Sigortası"
                    },
                    "comparison": "İlk tur daha geniş bir coğrafyayı kapsadığı için daha yüksek bir fiyata sahip. Fiyata dahil olan hizmetler arasında uçuşlar ve bazı turlar bulunuyor. İkinci tur daha uygun fiyatlı olsa da, ekstra turlar ve yemekler için ek bütçe ayırmanız gerekebilir. Her iki turda da vize ücreti ve seyahat sağlık sigortası fiyata dahil değil."
                },
                "tour_highlights": {
                    "tour1": {
                        "highlights": "İtalya'nın tarihi şehirleri, Yunanistan'ın antik kalıntıları ve Balkanların kültürel zenginlikleri. Roma, Floransa, Venedik gibi ikonik şehirleri ziyaret etme fırsatı.",
                        "tags": "Akdeniz, Alışveriş, Avrupa, Balayı, Balkan, Bulgar, Bulgaristan, Eğlence, Gece Hayatı, Gezi, Gurme, İtalya, Kültür, Moda, Müze, Romantik, Rumeli, Sanat, Sırbistan, Sırp, Tarih, Yeme - İçme, Yunan, Yunanistan, Yürüyüş"
                    },
                    "tour2": {
                        "highlights": "Yunanistan'ın en güzel adalarından Thassos'ta yüzme molası, Selanik'in tarihi ve kültürel mirası, Halkidiki'nin muhteşem plajları.",
                        "tags": "Ada, Adalar, Avrupa, Balayı, Balkan, Deniz, Eğlence, Gece Hayatı, Gezi, Güneş, Kum, Kültür, Romantik, Tarih, Yaz Tatili, Yeme - İçme, Yunan, Yunanistan"
                    },
                    "comparison": "İlk tur, Avrupa'nın kültürel ve tarihi zenginliklerini keşfetmek isteyenler için ideal. İkinci tur ise Yunanistan'ın deniz, kum ve güneşinin tadını çıkarmak isteyenler için daha cazip. Her iki tur da yerel lezzetleri tatma ve eğlence fırsatları sunuyor."
                },
                "ideal_for": {
                    "tour1": "Avrupa'yı kapsamlı bir şekilde gezmek isteyen, kültürel ve tarihi mekanlara ilgi duyan, uzun süreli bir tatil planlayan gezginler.",
                    "tour2": "Yunanistan'ın belirli bölgelerini keşfetmek isteyen, deniz tatili ve güneşlenmeyi seven, kısa süreli bir kaçamak arayan gezginler."
                },
                "pros_and_cons": {
                    "tour1": {
                        "pros": "Geniş bir coğrafyayı kapsıyor, birçok farklı kültürü deneyimleme fırsatı sunuyor, önemli tarihi ve turistik yerleri içeriyor.",
                        "cons": "Daha yüksek maliyetli, daha fazla seyahat süresi gerektiriyor, bazı öğünler ve aktiviteler fiyata dahil değil."
                    },
                    "tour2": {
                        "pros": "Daha uygun fiyatlı, kısa süreli bir tatil için ideal, Yunanistan'ın sahil şeridini ve adalarını keşfetme fırsatı sunuyor.",
                        "cons": "Daha az sayıda şehir ve ülke içeriyor, ekstra turlar ve yemekler için ek bütçe gerektiriyor."
                    }
                },
                "final_verdict": {
                    "recommendation": "Eğer Avrupa'yı kapsamlı bir şekilde keşfetmek ve farklı kültürleri deneyimlemek istiyorsanız, Viya Travel'ın 'Otobüs İle Büyük İtalya - Yunanistan - Balkan Turu' sizin için daha uygun olabilir. Ancak, Yunanistan'ın sahil şeridinde keyifli bir tatil geçirmek ve bütçenizi daha iyi kontrol etmek istiyorsanız, Mayak Tour'un 'Otobüs İle Selanik - Halkidiki - Thassos Turu'nu tercih edebilirsiniz. Kararınızı verirken, ilgi alanlarınızı, bütçenizi ve tatil sürenizi göz önünde bulundurmanızı öneririz."
                }
            },
            "modelNameAct": "gemini-2.0-flash",
            "source": "geminiAI",
            "sure": 9612,
            "posted_text": null
        }

        return new Promise(async (resolve, reject) => {
            let rBody;
            try {
                rBody = typeof req.body !== 'object' ? JSON.parse(req.body) : req.body;
            } catch (e) {
                console.log('error1', e.message);
            }
            console.log('req body', JSON.stringify(rBody))

            console.log('compareserver1', Date.now())
            await sleep(3000);
            console.log('compareserver2', Date.now())
            resolve(resp)
        });
    }
}


// Only export what's needed
module.exports = { fnPost, syncMongoToChroma, initializeChromaDB, getCollectionData, queryChromaDBwIntent };

// async function validateConnection() {
//   try {
//     await client.heartbeat();
//     return true;
//   } catch (error) {
//     console.error('ChromaDB server connection failed:', error);
//     return false;
//   }
// }

// async function _queryChromaDBwIntent(queryTextStg, intentData) {
//   const chromaClient = client;
//   try {
//     const queryText = normalizeText(queryTextStg);
//     console.log("Original query:", queryTextStg);
//     console.log("Normalized query:", queryText);
    
//     const queryEmbedding = await generateEmbedding(queryTextStg);
//     const collection = await chromaClient.getCollection({ name: collName });

//     const queryOptions = {
//       queryEmbeddings: [queryEmbedding],
//       nResults: 30, // Increased for better coverage
//       include: ["documents", "metadatas", "distances"],
//     };

//     console.log("Fetching initial results...");
//     let queryResults = await collection.query(queryOptions);
//     console.log(`Found ${queryResults.ids?.[0]?.length || 0} initial results`);
    
//     if (queryResults.ids?.[0]?.length > 0) {
//       const matches = [];
//       const queryWords = queryText.split(' ');
      
//       // Debug log
//       console.log("Searching for words:", queryWords);
      
//       queryResults.metadatas[0].forEach((metadata, idx) => {
//         // Create searchable text combining all relevant fields
//         const searchText = normalizeText([
//           metadata.title,
//           metadata.cities,
//           metadata.program,
//           metadata.details,
//           metadata.notes,
//           queryResults.documents[0][idx]
//         ].join(' '));
        
//         // Debug each document
//         console.log(`\nChecking document ${idx}:`);
//         console.log("Title:", metadata.originalTitle);
//         console.log("Cities:", metadata.originalLocation);
//         console.log("Normalized search text sample:", searchText.substring(0, 100));
        
//         // Check for partial matches
//         const matchedWords = queryWords.filter(word => {
//           const isMatch = searchText.includes(word);
//           console.log(`Word "${word}": ${isMatch ? 'FOUND' : 'not found'}`);
//           return isMatch;
//         });
        
//         // Accept if most words match (>50%)
//         if (matchedWords.length > 0 && matchedWords.length >= queryWords.length * 0.5) {
//           matches.push({
//             index: idx,
//             matchCount: matchedWords.length,
//             distance: queryResults.distances[0][idx]
//           });
//           console.log("✓ Document matched!");
//         }
//       });

//       // Sort matches by match count and distance
//       matches.sort((a, b) => 
//         b.matchCount - a.matchCount || a.distance - b.distance
//       );
      
//       if (matches.length > 0) {
//         console.log(`\nFound ${matches.length} matches`);
//         const sortedIndices = matches.map(m => m.index);
        
//         return {
//           ids: [sortedIndices.map(idx => queryResults.ids[0][idx])],
//           metadatas: [sortedIndices.map(idx => queryResults.metadatas[0][idx])],
//           documents: [sortedIndices.map(idx => queryResults.documents[0][idx])],
//           distances: [sortedIndices.map(idx => queryResults.distances[0][idx])]
//         };
//       }
//     }
    
//     console.log("\nNo matching documents found");
//     return false;

//   } catch (error) {
//     console.error("Error querying ChromaDB:", error);
//     throw error;
//   }
// }
// // Refactored queryChromaDB function
// async function fnqueryChromaDB(collectionName, queryOptions) {
//   try {
//     // Check for required parameters
//     if (!queryOptions || !queryOptions.queryEmbeddings) {
//       throw new Error("queryEmbeddings are required in queryOptions.");
//     }
//     // Get the "tours" collection
//     if (!await validateConnection()) {
//       throw new Error('ChromaDB server is not available');
//     }

//     // Get the collection
//     const collection = await client.getCollection({ name: collectionName });

//     // Prepare query parameters with defaults
//     const queryParams = {
//       queryEmbeddings: queryOptions.queryEmbeddings,
//       nResults: queryOptions.nResults || 10, // Default to 10 results
//     };

//     if (queryOptions.where) {
//       queryParams.where = queryOptions.where;
//     }

//     if (queryOptions.whereDocument) {
//       queryParams.whereDocument = queryOptions.whereDocument;
//     }

//     // Perform the query
//     const results = await collection.query(queryParams);
//     return results;

//   } catch (error) {
//     console.error("Error querying ChromaDB:", error);
//     throw error; // Re-throw the error for the caller to handle
//   }
// }
// async function queryChromaDB(queryTextStg) {
//   try {
//     function preprocessText(text) {
//       return text.toLowerCase().replace(/[^a-z0-9\s]/g, ''); // Remove special characters
//     }
    
//     //"Which tour is best for cultural experiences?"
//     var queryText = preprocessText(queryTextStg);
//     console.log("Query text:", queryTextStg, queryText);
//     const queryEmbedding = await generateEmbedding(queryText);
//     const collectionName = collName; // Replace with your collection name
//     const queryOptions = {
//       queryEmbeddings: [queryEmbedding],
//       nResults: 5,
//       // where: { "author": "Shakespeare" }, // Optional filter
//       // whereDocument: { "$contains": "king" } // Optional text filter
//     };

//     try {
//       const queryResults = await fnqueryChromaDB(collectionName, queryOptions);
//       // console.log("Query results:", queryResults);
  
//       if (queryResults.ids && queryResults.ids.length > 0) {
//           console.log("Found the following IDs:", queryResults.ids);
//           // console.log("Documents:", queryResults.documents);
//           // console.log("Metadatas:", JSON.stringify(queryResults.metadatas));
//           // console.log("Distances:", queryResults.distances);

//         return queryResults;

//       } else {
//         console.log("No matching documents found.");
//         return false
//       }
  
//     } catch (error) {
//       console.error("Failed to run the example:", error);
//       return false
//     }

//   } catch (error) {
//     console.error('Error querying ChromaDB:', error);
//     throw error;
//   }
// }

// async function getCollectionDataCount(collname = 'tours') {
//   try {
//     if (await checkCollectionExists(collName, false, false)) {
       
//       const collection = await client.getCollection({ name: collName });
//       const data = await collection.get();
//       return data
//       // Process tours in batches
      
//     } else {
//       console.log('ChromaDB tasd not found');
//       return (false)
//     }
//     // 
    
//   } catch (error) {
//     console.error('Fatal error initializing ChromaDB:', error);
//     throw error;
//   }
// }

// // Query ChromaDB function
// async function queryChromaDBwIntent_(queryTextStg, intentData) {
//   const chromaClient = client;
//   try {
//     function preprocessText(text) {
//       return text.toLowerCase().replace(/[^a-z0-9\s]/g, "");
//     }

//     const queryText = preprocessText(queryTextStg);
//     console.log("Query text:", queryTextStg, queryText);

//     const queryEmbedding = await generateEmbedding(queryText);
//     console.log("Query queryEmbedding:", [...queryEmbedding].slice(-3));

//     // Query options with basic parameters
//     const queryOptions = {
//       queryEmbeddings: [queryEmbedding],
//       nResults: 5,
//     };

//     // Only add filters if we have valid data
//     if (intentData?.details) {
//       const whereClause = {};
      
//       if (intentData.details.activities && Array.isArray(intentData.details.activities) && intentData.details.activities.length > 0) {
//         whereClause.location = intentData.details.activities[0]; // Using first activity as location filter
//       }
      
//       if (Object.keys(whereClause).length > 0) {
//         queryOptions.where = whereClause;
//       }
//     }

//     const collection = await chromaClient.getCollection({ name: collName });
//     const queryResults = await collection.query(queryOptions);

//     if (queryResults.ids && queryResults.ids.length > 0) {
//       console.log("Found the following IDs:", queryResults.ids);
//       console.log("Metadatas:", JSON.stringify(queryResults.metadatas));
//       return queryResults;
//     } else {
//       console.log("No matching documents found.");
//       return false;
//     }
//   } catch (error) {
//     console.error("Error querying ChromaDB:", error);
//     throw error;
//   }
// }


// const HUGGINGFACE_API_URL = 'https://api-inference.huggingface.co/models/BAAI/bge-small-en-v1.5';
// const API_KEY = '*************************************';

// async function generateEmbedding_local(text) {
//   if (!text || typeof text !== 'string') {
//     return Array.from({ length: 1536 }, () => 0.0);
//   }

//   try {
//     // Create fixed-size embedding (1536 dimensions)
//     const embedding = Array.from({ length: 1536 }, () => 0.0);
//     const words = text.split(/\s+/).filter(Boolean);
    
//     // Generate consistent embeddings based on word position
//     for (let i = 0; i < Math.min(words.length, 1536); i++) {
//       embedding[i] = (i + 1) / 1536; // Normalize between 0 and 1
//     }
    
//     return embedding;
//   } catch (error) {
//     console.error('Error generating embedding:', error);
//     return Array.from({ length: 1536 }, () => 0.0);
//   }
// }

// async function queryChromaDB_local(queryTextStg) {
//   try {
//     if (!await validateConnection()) {
//       return { error: 'ChromaDB server is not available' };
//     }
//     var queryText;
//     if (Array.isArray(queryTextStg) && queryTextStg.length !== 0) {
//       queryText = queryTextStg.slice(-1)[0]?.content;
//     } else {
//       queryText = '';
//     }

//     const normalizedQuery = (queryText || '').toString().trim();
//     if (!normalizedQuery) {
//       return { error: 'Empty query' };
//     }

//     const collection = await client.getCollection({ name: collName });
//     if (!collection) {
//       return { error: 'Collection not found' };
//     }

//     const count = await collection.count();
//     if (count === 0) {
//       return { error: 'Collection is empty' };
//     }

//     const queryEmbedding = await generateEmbedding(normalizedQuery);
    
//     // // Ensure embedding is valid
//     // if (!Array.isArray(queryEmbedding) || queryEmbedding.length !== 1536) {
//     //   return { error: 'Invalid embedding dimension' };
//     // }

//     const queryRequest = {
//       queryEmbeddings: [queryEmbedding],
//       nResults: 3
//     };

//     const results = await collection.query(queryRequest);
//     console.log('normalizedQuery', normalizedQuery)
//     console.log('normalizedQuery results', results)
//     return {
//       ids: results?.ids?.[0] || [],
//       distances: results?.distances?.[0] || [],
//       metadatas: results?.metadatas?.[0] || [],
//       documents: results?.documents?.[0] || []
//     };

//   } catch (error) {
//     console.error('Error querying ChromaDB:', error);
//     return {
//       error: error.message,
//       ids: [],
//       distances: [],
//       metadatas: [],
//       documents: []
//     };
//   }
// }async function generateEmbedding_hugging(text) {
//   try {
//     const response = await fetch(
//       `${HUGGINGFACE_API_URL}`,
//       {
//         method: 'POST',
//         headers: {
//           Authorization: `Bearer ${API_KEY}`,
//           'Content-Type': 'application/json',
//         },
//         body: JSON.stringify({ inputs: text }),
//       }
//     );

//     // Check if the response is OK (status code 200-299)
//     if (!response.ok) {
//       console.log('response', response)
//       throw new Error(`HTTP error! Status: ${response.status}`);
//     }

//     // Parse the response as JSON
//     const data = await response.json();

//     // Ensure the response is an array
//     const embedding = Array.isArray(data) ? data : [];
//     return embedding;
//   } catch (error) {
//     console.error('Error generating embedding:', error);
//     throw error;
//   }
// }

// async function generateEmbedding_(text) {
//   try {
//     const response = await fetch('http://127.0.0.1:11434/api/embed', { // Doğru endpoint
//       method: 'POST',
//       headers: {
//         'Content-Type': 'application/json',
//       },
//       body: JSON.stringify({
//         model: 'nomic-embed-text:latest', // Kullanılacak model
//         input: text, // Embedding oluşturmak için metin
//       }),
//     });

//     // Check if the response is OK (status code 200-299)
//     if (!response.ok) {
//       const errorDetails = await response.json(); // Hata detaylarını al
//       throw new Error(`HTTP error! Status: ${response.status}, Details: ${JSON.stringify(errorDetails)}`);
//     }

//     // Yanıtı JSON olarak parse et
//     const data = await response.json();

//     // Ensure the embedding is a flat array
//     let embedding = data.embeddings;

//     if (Array.isArray(embedding[0])) {
//       embedding = embedding.flat();
//     }

//     // Embedding'i döndür
//     return embedding;
//   } catch (error) {
//     console.error('Error generating embedding:', error);
//     throw error;
//   }
// }

// // // Örnek kullanım
// // (async () => {
// //   const text = "This is a test sentence.";
// //   const embedding = await generateEmbedding(text);
// //   console.log('Generated Embedding:', embedding);
// // })();
