// const { ChromaClient } = require('chromadb'); 
import { ChromaClient } from "chromadb";
// const client = new ChromaClient();

const client = new ChromaClient();


const collName = 'tours';

// async function initializeCollection(collname = 'tours') {
//   try {
//     // "tours" koleksiyonunu oluştur
//     console.log(`creating Collection "${collname}"..`);
//     const collection = await client.createCollection({ name: collname });
//     console.log(`Collection "${collname}" created successfully..`);
//     return true;
//   } catch (error) {
//     console.error('Error creating collection:', error);
//     return false
//   }
// }

async function checkCollectionExists(collname = 'tours') {
  try {
    const collections = await client.listCollections();
    console.log('Available collections:', collections.map(coll => coll.name));
    // Check if the collection with the given name exists
    console.log(`Checking if collection "${collname}" exists...`);
    if (!collections || collections.length === 0) {
      console.log('No collections found.');
      return false; // No collections found
    } else {
      console.log('Collections found.');
    }
    return collections.some(coll => coll.name === collname);
  } catch (error) {
    console.error('Error checking collection existence:', error);
    return false;
  }
}

async function main() { 
  try {
    if (!await checkCollectionExists(collName)) {
      // await initializeCollection(collName);
    }
  } catch (error) {
    console.error('Fatal error initializing ChromaDB:', error);
    throw error;
  }
}

main();
