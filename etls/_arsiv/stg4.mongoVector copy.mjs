import sqlite3 from 'sqlite3';
import { open } from 'sqlite';
import fs from 'fs';
import path from 'path';
import { MongoClient } from 'mongodb';
import dotenv from 'dotenv';
dotenv.config();
import { GoogleGenerativeAI } from '@google/generative-ai';
// import { pipeline, env } from '@xenova/transformers';
// .env dosyasını yükle

// WebAssembly için wasm yollarını ayarlayalım
// env.backends.onnx.wasm.wasmPaths = 'https://onnxruntime.github.io/onnx-js-demo/dist/ ';

export const vectorEmbeddings = {
    google: async ({
        text,
        genAIpack,
        model = "text-embedding-004",
    }) => {
        if (!text) {
            console.error('google: Error no text');
            return null
        };
        try {
            // API anahtarı ile Google AI istemcisini başlat
            const GEMINI_API_KEY = process.env.GEMINI_API_KEY_YODA;
            const genAI = genAIpack || new GoogleGenerativeAI(GEMINI_API_KEY);
            // text-embedding-004 modelini al
            const embeddingModel = genAI.getGenerativeModel({ model });
            // Embedding oluştur
            const result = await embeddingModel.embedContent(text);
            // Embedding değerlerini çıkar
            if (result && result.embedding && Array.isArray(result.embedding.values)) {
                return result.embedding.values;
            } else {
                console.error('Unexpected response format from Google AI:', result);
                return null;
            }
        } catch (error) {
            console.error('Error generating embedding with Google AI:', error);
            return null;
        }
    },
    ollama: async ({
        text,
        uri = 'http://127.0.0.1:11434/api/embeddings',
        model = "mxbai-embed-large:latest",
    }) => {
        if (!text) {
            console.error('ollama: Error no text', text);
            return null
        };

        try {
            const response = await fetch(uri, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    model, prompt: text,
                }),
            });

            if (!response.ok) {
                const errorData = await response.json();
                console.error('Ollama embedding error:', errorData);
                return null;
            }

            const data = await response.json();
            return data.embedding;
        } catch (error) {
            console.error('Error generating embedding with Ollama:', error);
            return null;
        }
    },
    bertTurkish: async ({ text }) => {
        if (!text) {
            console.error('bertTurkish: Error no text', text);
            return null;
        }

        try {
            const response = await fetch('http://127.0.0.1:8080/embed', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ text }),
            });
            if (!response.ok) {
                const errorData = await response.json();
                console.error('BERT Turkish Service Error:', errorData);
                return null;
            }

            const data = await response.json();
            return data.embedding; // Float32Array değilse zaten array gelir
        } catch (error) {
            console.error('Error generating Turkish BERT embedding:', error);
            return null;
        }
    },
    // bertTurkishx: async ({ text }) => {
    //     if (!text) {
    //         console.error('bertTurkish: Error no text', text);
    //         return null;
    //     }

    //     try {
    //         // Model zaten cached olabilir; tekrar yüklememek için singleton yapısı da kullanılabilir
    //         const extractor = await pipeline('feature-extraction', 'dbmdz/bert-base-turkish-cased');

    //         // Embedding çıkarma işlemi (normalize ve mean pooling ile)
    //         const output = await extractor(text, {
    //             pooling: 'mean',
    //             normalize: true
    //         });

    //         // Float32Array -> Array<number> çevir
    //         return Array.from(output.data);
    //     } catch (error) {
    //         console.error('Error generating Turkish BERT embedding:', error);
    //         return null;
    //     }
    // }
}

function normalizeText(text) {
    if (!text || typeof text !== 'string') return '';
    // HTML kod bloklarını kaldır
    let cleaned = text; // text.replace(/<[^>]*>/g, ' ');
    // HTML escape karakterlerini karşılıklarıyla değiştir
    cleaned = cleaned
        .replace(/&nbsp;/gi, ' ')
        .replace(/&amp;/gi, '&')
        .replace(/&lt;/gi, '<')
        .replace(/&gt;/gi, '>')
        .replace(/&quot;/gi, '"')
        .replace(/&#39;/gi, "'")
        .replace(/&apos;/gi, "'")
        // Diğer tüm &xxxx; biçimindekileri boşlukla değiştir
        .replace(/&[a-zA-Z0-9#]+;/g, ' ');

    // HTML kod bloklarını kaldır
    cleaned = cleaned.replace(/<[^>]*>/g, ' ');
    // \t, \n, \r ve kontrol karakterlerini temizle (0x00-0x1F ve 0x7F)
    cleaned = cleaned.replace(/[\t\n\r]/g, ' ');
    cleaned = cleaned.replace(/[\u0000-\u001F\u007F]/g, '');
    // Fazla boşlukları tek boşluğa indir
    cleaned = cleaned.replace(/\s+/g, ' ');
    // Önce Türkçe karakterleri eşlenik İngilizce karakterlerle değiştir
    cleaned = cleaned
        .replace(/ç/g, 'c')
        .replace(/Ç/g, 'C')
        .replace(/ğ/g, 'g')
        .replace(/Ğ/g, 'G')
        .replace(/ı/g, 'i')
        .replace(/İ/g, 'I')
        .replace(/ö/g, 'o')
        .replace(/Ö/g, 'O')
        .replace(/ş/g, 's')
        .replace(/Ş/g, 'S')
        .replace(/ü/g, 'u')
        .replace(/Ü/g, 'U');
    // Sonra lowercase dönüşümü
    cleaned = cleaned.toLowerCase();
    return cleaned.trim();
}

function prepareVectorText(object, fields = []) {
    // Seçili alanları normalize edip birleştir
    const parts = fields
        .filter(field => Object.prototype.hasOwnProperty.call(object, field))
        .map(field => normalizeText(object[field] || ''));
    const combined = parts.join(' ');
    return normalizeText(combined);
}

async function main() {
    const dtBop = Date.now();

    try {
        const tables2Sync = [
            {
                mongoCollection: 'tourai.data.dim.hotelsdomestic',
                needsVectorEmbedding: true,
                embeddingFields: ['TESIS_ADI', 'KATEGORI', 'BOLGE_ADI', 'ALT_BOLGE_ADI', 'BOLGE_DETAY', 'ACIKLAMA', 'KONAKLAMA_ACIKLAMA']
            },
            // {
            //     mongoCollection: 'tourai.data.dim.hotelsdomestic_summary',
            //     needsVectorEmbedding: false,
            //     embeddingFields: ['TESIS_ADI', 'KATEGORI', 'BOLGE_ADI', 'ALT_BOLGE_ADI', 'BOLGE_DETAY', 'ACIKLAMA', 'KONAKLAMA_ACIKLAMA']
            // },
        ];

        // MongoDB bağlantısı
        const mongoUri = process.env.MONGODB_TOUR_URI;
        if (!mongoUri) {
            console.error('💥 MONGODB_TOUR_URI environment variable bulunamadı!');
            process.exit(1);
        }

        console.log('🔗 MongoDB\'ye bağlanılıyor...');
        const mongoClient = new MongoClient(mongoUri);
        await mongoClient.connect();
        console.log('✅ MongoDB bağlantısı başarılı');

        const mongoDb = mongoClient.db('toursdb');

        // Vector Embedding işlemleri
        console.log(`\n🤖 VECTOR EMBEDDING İŞLEMLERİ`);
        console.log(`=====================================`);

        const vectorResults = [];
        const vectorCollections = tables2Sync.filter(table => table.needsVectorEmbedding);

        for (let i = 0; i < vectorCollections.length; i++) {
            const tableInfo = vectorCollections[i];
            const vectorStartTime = Date.now();

            console.log(`\n[${i + 1}/${vectorCollections.length}] ${tableInfo.mongoCollection} için vector embedding işlemi başlıyor...`);

            try {
                const collection = mongoDb.collection(tableInfo.mongoCollection);

                // Koleksiyondaki toplam kayıt sayısını al
                const totalRecords = await collection.countDocuments();
                if (totalRecords === 0) {
                    console.log(`  ⚠️  ${tableInfo.mongoCollection} koleksiyonunda kayıt bulunamadı, vector embedding atlanıyor...`);
                    vectorResults.push({
                        collection: tableInfo.mongoCollection,
                        processedCount: 0,
                        duration: Date.now() - vectorStartTime,
                        status: 'skipped'
                    });
                    continue;
                }

                // Kayıtları batch'ler halinde işle
                const batchSize = 500; // Embedding API limitleri için küçük batch
                let processedCount = 0;
                let successCount = 0;
                let errorCount = 0;

                console.log(`  📊 ${totalRecords} kayıt bulundu`);
                console.log(`  🔄 Vector embedding'ler oluşturuluyor..., ${batchSize} kadarlık batch'lerde...`);

                // Tüm kayıtları al
                const allRecords = await collection.find({}).toArray();

                for (let j = 0; j < allRecords.length; j += batchSize) {
                    const batch = allRecords.slice(j, j + batchSize);

                    for (const record of batch) {
                        try {
                            // Embedding alanlarını birleştir
                            const vectorText = prepareVectorText(record, tableInfo.embeddingFields);

                            if (!vectorText || vectorText.trim() === '') {
                                processedCount++;
                                continue;
                            }

                            // Embedding oluştur (BERT Turkish kullan)
                            const embedding = await vectorEmbeddings.bertTurkish({ text: vectorText });

                            if (embedding && Array.isArray(embedding)) {
                                // Embedding'i kayıta ekle
                                await collection.updateOne(
                                    { _id: record._id },
                                    {
                                        $set: {
                                            vectorEmbedding: embedding,
                                            embeddingText: vectorText.substring(0, 500), // İlk 1000 karakter
                                            embeddingCreatedAt: new Date()
                                        }
                                    }
                                );
                                successCount++;
                            } else {
                                errorCount++;
                            }

                            processedCount++;

                            // İlerleme göster
                            if (processedCount % batchSize === 0 || processedCount === totalRecords) {
                                const percentage = ((processedCount / totalRecords) * 100).toFixed(1);
                                console.log(`    📊 ${processedCount}/${totalRecords} kayıt işlendi (${percentage}%) - Başarılı: ${successCount}, Hatalı: ${errorCount}`);
                            }

                            // API rate limit için kısa bekleme
                            await new Promise(resolve => setTimeout(resolve, 100));

                        } catch (recordError) {
                            errorCount++;
                            processedCount++;
                            console.log(`    ⚠️  Kayıt işleme hatası: ${recordError.message}`);
                        }
                    }
                }

                const vectorElapsedTime = Date.now() - vectorStartTime;
                console.log(`  ✅ Vector embedding tamamlandı. İşlenen: ${processedCount}, Başarılı: ${successCount}, Hatalı: ${errorCount} (${vectorElapsedTime}ms)`);

                vectorResults.push({
                    collection: tableInfo.mongoCollection,
                    processedCount: processedCount,
                    successCount: successCount,
                    errorCount: errorCount,
                    duration: vectorElapsedTime,
                    status: 'completed'
                });

            } catch (vectorError) {
                const vectorElapsedTime = Date.now() - vectorStartTime;
                console.error(`  ❌ ${tableInfo.mongoCollection} vector embedding hatası: ${vectorError.message} (${vectorElapsedTime}ms)`);

                vectorResults.push({
                    collection: tableInfo.mongoCollection,
                    processedCount: 0,
                    successCount: 0,
                    errorCount: 0,
                    duration: vectorElapsedTime,
                    status: 'error',
                    error: vectorError.message
                });
            }
        }

        // Özet raporu
        const totalElapsedTime = Date.now() - dtBop;

        // Vector Embedding özeti
        if (vectorResults.length > 0) {
            console.log(`\n🤖 VECTOR EMBEDDING ÖZETİ`);
            console.log(`=====================================`);

            let totalVectorProcessed = 0;
            let totalVectorSuccess = 0;
            let totalVectorErrors = 0;
            let vectorSuccessCount = 0;
            let vectorErrorCount = 0;
            let vectorSkippedCount = 0;

            vectorResults.forEach((result, index) => {
                const statusIcon = result.status === 'completed' ? '✅' :
                                  result.status === 'error' ? '❌' : '⏭️';

                console.log(`  ${statusIcon} [${index + 1}] ${result.collection}`);
                console.log(`      📊 İşlenen kayıt: ${result.processedCount.toLocaleString()}`);
                console.log(`      ✅ Başarılı embedding: ${result.successCount || 0}`);
                console.log(`      ❌ Hatalı embedding: ${result.errorCount || 0}`);
                console.log(`      ⏱️  Süre: ${result.duration}ms (${(result.duration/1000).toFixed(2)}s)`);

                if (result.error) {
                    console.log(`      ⚠️  Hata: ${result.error}`);
                }

                totalVectorProcessed += result.processedCount || 0;
                totalVectorSuccess += result.successCount || 0;
                totalVectorErrors += result.errorCount || 0;

                if (result.status === 'completed') vectorSuccessCount++;
                else if (result.status === 'error') vectorErrorCount++;
                else vectorSkippedCount++;
            });

            console.log(`\n🤖 VECTOR EMBEDDING İSTATİSTİKLERİ`);
            console.log(`=====================================`);
            console.log(`✅ Başarılı koleksiyon: ${vectorSuccessCount}`);
            console.log(`❌ Hatalı koleksiyon: ${vectorErrorCount}`);
            console.log(`⏭️  Atlanan koleksiyon: ${vectorSkippedCount}`);
            console.log(`📊 Toplam işlenen kayıt: ${totalVectorProcessed.toLocaleString()}`);
            console.log(`✅ Başarılı embedding: ${totalVectorSuccess.toLocaleString()}`);
            console.log(`❌ Hatalı embedding: ${totalVectorErrors.toLocaleString()}`);
            if (totalVectorProcessed > 0) {
                const vectorSuccessRate = ((totalVectorSuccess / totalVectorProcessed) * 100).toFixed(1);
                console.log(`📈 Başarı oranı: ${vectorSuccessRate}%`);
            }

            console.log(`\n⏱️  TOPLAM SÜRE: ${totalElapsedTime}ms (${(totalElapsedTime/1000).toFixed(2)}s)`);
            if (totalVectorSuccess > 0) {
                const avgTimePerEmbedding = (totalElapsedTime / totalVectorSuccess).toFixed(2);
                console.log(`⚡ Ortalama embedding süresi: ${avgTimePerEmbedding}ms/embedding`);
            }
        }

        console.log(`\n🎉 Tüm vector embedding işlemleri tamamlandı!`);

        // Bağlantıyı kapat
        await mongoClient.close();
        // process.exit(1);
        // await mongoClient.close();
    } catch (error) {
        console.error('💥 Genel hata oluştu:', error);
        process.exit(1);
    }
}

// Script'i çalıştır
main();
