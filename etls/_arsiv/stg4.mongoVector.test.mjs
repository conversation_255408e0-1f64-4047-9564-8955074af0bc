import { GoogleGenAI } from "@google/genai";
import dotenv from 'dotenv';
dotenv.config();
async function main() {

    const GEMINI_API_KEY = process.env.GOOGLE_AI_API_KEY;
    const ai = new GoogleGenAI({
                apiKey: GEMINI_API_KEY,
            });

    const response = await ai.models.embedContent({
        model: 'gemini-embedding-exp-03-07',
        contents: 'What is the meaning of life?',
        config: {
            taskType: "SEMANTIC_SIMILARITY",
        }
    });

    console.log(response.embeddings);
}

main();