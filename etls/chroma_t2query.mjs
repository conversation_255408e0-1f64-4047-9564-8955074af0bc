// const { ChromaClient } = require('chromadb'); 
import { GoogleGenerativeAI, GenerativeModel } from "@google/generative-ai";
import { ChromaClient } from "chromadb";
import axios from 'axios';
import dotenv from 'dotenv';
// .env dosyasını yükle
dotenv.config();
const client = new ChromaClient();
const CHROMA_API_URL = 'http://localhost:8000';
const collectionName = 'q_summary_vector'; // Default collection name 

const useLocalModel = {
  forEmbedding: true,
  forMetadataExtraction: false,
  forQuery: true,
};
const fnChroma = {
  listCollections: async (props = {}) => {
    const {chromaClient = client} = props;
    try {
      let collectionstg = await chromaClient.listCollections();
      let collections = collectionstg.map(coll => coll.name);
      // console.log('Available collections:', collections.map(coll => coll.name));
      return collections;
    } catch (error) {
      // console.error('Error listing collections:', error);
      return [];
    }
  },

  deleteCollection: async  (props = {}) => {
    const { collname = 'tours', chromaClient = client } = props;
    try {
      await chromaClient.deleteCollection({ name: collname });
      console.log(`Collection "${collname}" deleted successfully.`);
      return true;
    } catch (error) {
      throw `Error deleting "${collname}" collection. ${error.message}`;
    }
  },
  collectionExists: async ({chromaClient = client, collname = 'tours'}) => {
    try {
      const collections = await client.listCollections();
      console.log('Available collections:', collections.map(coll => coll.name));
      // Check if the collection with the given name exists
      console.log(`Checking if collection "${collname}" exists...`);
      if (!collections || collections.length === 0) {
        console.log('No collections found.');
        return false; // No collections found
      } else {
        console.log('Collections found.');
      }
      return collections.some(coll => coll.name === collname);
    } catch (error) {
      console.error('Error checking collection existence:', error);
      return false;
    }
  },

  createColl: async ({
    collname= 'noname', dropIfExists = false, chromaClient = client, embedDimension = 768, debug = false
  }) => {
    try {
      //check if collection exists

      let collection;
      const collections = await fnChroma.listCollections({ chromaClient });
      if (collections.includes(collname)) {
        if (dropIfExists) {
          debug && console.log(`Collection "${collname}" already exists. Deleting it...`);
          await fnChroma.deleteCollection({ collname, chromaClient });
        } else {
          debug && console.log(`Collection "${collname}" already exists. Skipping creation.`);
          return true; // Collection already exists, no need to create
        }
      }

      debug && console.log(`Creating collection "${collname}"...`);
      collection = await chromaClient.createCollection({
        name: collname,
        embeddingFunction: null, // Explicitly set to null for compatibility
        metadata: {
          "hnsw:space": "cosine",
          "hnsw:construction_ef": 100,
          "hnsw:search_ef": 100,
          "hnsw:M": 16,
          "dimension": embedDimension // Specify the expected embedding dimension
        }
      });
      debug && console.log(`Collection "${collname}" created successfully..`, collection);
      return true;
    } catch (error) {
      debug && console.error('Error creating collection:', error);
      // return true;
      throw `Error creating "${collname}" collection. ${error.message}`;
    }
  },

  addrec2coll: async (data, collname = collectionName) => {
    try {
      const collection = await client.getCollection({ name: collname });
      await collection.add(data);
      console.log(`Records added to collection "${collname}" successfully..`, data);
      return true;
    } catch (error) {
      console.error('Error adding to collection:', error);
      return false;
    }
  },
  queryCollection: async ({queryText, metadata = {}, debug = false}) => {
    let fnEmbedding = useLocalModel.forEmbedding ? modelQueries.ollama.vectorEmbeddings : modelQueries.ollama.vectorEmbeddings;
    const embedding = await fnEmbedding({ text: queryText });
    try {
      const queryOptions = {
        queryEmbeddings: [embedding],
        nResults: 5,
      };

      // Initialize metadata filter
      if (metadata && Object.keys(metadata).length > 0) {
        const metadataFilter = [];
        const metaFilters = [];
        // const mmetadataFilter = [];
        for (const key in metadata) {
          const values = typeof metadata[key] === 'string' ? fnSupport.normalizeText(metadata[key]) : metadata[key];
          values && values !== 'null' && metadataFilter.push({ $contains: values });
        }

        if (metadataFilter.length !== 0) {
          queryOptions.whereDocument = { $or: metadataFilter }; // Default filter if no metadata is provided
        }

        const metadataStg = fnSupport.normalizeJsonObject({ ...metadata });
        const filterMap = {
          country: { BOLGE_ADI: metadataStg.country, BOLGE_DETAY: metadataStg.country, ALT_BOLGE_ADI: metadataStg.country },
          region: { BOLGE_ADI: metadataStg.region, BOLGE_DETAY: metadataStg.region, ALT_BOLGE_ADI: metadataStg.region },
          city: { BOLGE_ADI: metadataStg.city, BOLGE_DETAY: metadataStg.city, ALT_BOLGE_ADI: metadataStg.city }
        };

        for (const key in filterMap) {
          if (metadataStg[key]) {
            const value = metadataStg[key];
            const filterConditions = Object.entries(filterMap[key]).map(([field, metaValue]) => ({
              [field]: { $in: [value] }
            }));
            metaFilters.push(...filterConditions);
          }
        }
        // console.log('Metadata filters:', metaFilters);

        const whereFilter = {}
        if (metadataStg.category) {
          if (metaFilters.length > 0) {
            whereFilter.$and = [{ KATEGORI: { $in: [metadataStg.category] } }, { $or: metaFilters }];
          } else {
            whereFilter.KATEGORI = { $in: [metadataStg.category] };
          }
        } else {
          if (metaFilters.length > 0) {
            whereFilter.$or = metaFilters; // If no category, use only metadata filters
          }
        }
        // console.log('Final where filter:', JSON.stringify(whereFilter, null, 2));
        queryOptions.where = whereFilter;
      }

      const collection = await client.getCollection({ name: collectionName });
      const queryResults = await collection.query(queryOptions);

      delete queryOptions.queryEmbeddings; // Remove embeddings from options for logging
      debug && console.log('Querying ChromaDB with following options:', JSON.stringify(queryOptions, null, 2));

      debug && console.log(queryResults);
      return queryResults;
    } catch (error) {
      console.error("❌ Arama hatası:", error.message);
      return null;
    }
  },
  getAllRecords: async (props = {}) => {
    const { collname = collectionName, chromaClient = client, limit = 1000 } = props;
    try {
      const collection = await chromaClient.getCollection({ name: collname });
      const results = await collection.get();
      return results;
    } catch (error) {
      console.error('Veriler alınırken hata oluştu:', error.message);
      if (error.response) console.error(error.response.data);
      return [];
    }
  },
};

const fnSupport = {
  normalizeJsonObject: (jsonObj) => {
    const normalizedObj = {};
    for (const key in jsonObj) {
      if (Object.prototype.hasOwnProperty.call(jsonObj, key)) {
        let value = jsonObj[key];
        // Convert null or undefined to empty string before normalization
        if (value === null || value === undefined) {
          value = '';
        }
        normalizedObj[key] = typeof value === 'string' ? fnSupport.normalizeText(value) : value;
      }
    }
    return normalizedObj;
  },
  extractAndParseJSON: (responseText) => {
    try {
      // Markdown kod bloğu işaretlerini (`json` ve ``` işaretlerini) temizleyin
      const jsonRegex = /```json\s*([\s\S]*?)\s*```/; // JSON içeriğini bulmak için regex
      const match = responseText.match(jsonRegex);

      if (!match || !match[1]) {
        throw new Error("Yanıtta geçerli bir JSON bulunamadı.");
      }

      const jsonString = match[1].trim(); // JSON içeriğini al

      // JSON'ı parse edin
      const parsedJSON = JSON.parse(jsonString);
      return parsedJSON;
    } catch (error) {
      console.error("JSON extraction veya parsing hatası:", error.message);
      try {
        let jsonString = responseText;
        const parsedJSON = JSON.parse(jsonString);
        return parsedJSON;
      } catch (e) {
        console.error("JSON extraction veya parsing hatası:", e.message);
        // throw error;
        return responseText;
      }
    }
  },
  normalizeText: (text) => {
    if (!text || typeof text !== 'string') return '';
    let cleaned = text;
    // HTML etiketleri ve özel karakterler temizleniyor
    cleaned = cleaned
        .replace(/<[^>]*>/g, ' ')
        .replace(/&nbsp;/gi, ' ')
        .replace(/&amp;/gi, '&')
        .replace(/</gi, '<')
        .replace(/>/gi, '>')
        .replace(/&quot;/gi, '"')
        .replace(/&#39;/gi, "'")
        .replace(/&apos;/gi, "'")
        .replace(/&[a-zA-Z0-9#]+;/g, ' ')
        .replace(/[\t\n\r]/g, ' ')
        .replace(/[\u0000-\u001F\u007F]/g, '')
        .replace(/\s+/g, ' ');

    // Türkçe karakter dönüşümü
    cleaned = cleaned
        .replace(/ç/g, 'c').replace(/Ç/g, 'C')
        .replace(/ğ/g, 'g').replace(/Ğ/g, 'G')
        .replace(/ı/g, 'i').replace(/İ/g, 'I')
        .replace(/ö/g, 'o').replace(/Ö/g, 'O')
        .replace(/ş/g, 's').replace(/Ş/g, 'S')
        .replace(/ü/g, 'u').replace(/Ü/g, 'U');

    return cleaned.toLowerCase();
  },
};

const modelQueries = {
  ollama: {
    vectorEmbeddings: async ({ text, uri, model, }) => {
      uri = uri || 'http://127.0.0.1:11434/api/embeddings';
      model = model || "nomic-embed-text:latest";

      if (!text) {
        console.error('modelQueries - ollama: Error no text', text);
        return null;
      }

      try {
        const response = await fetch(uri, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            model,
            prompt: text,
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          console.error('modelQueries - Ollama embedding error:', errorData);
          return null;
        }

        const data = await response.json();
        return data.embedding;
      } catch (error) {
        console.error('Error generating embedding with modelQueries - Ollama:', error);
        return null;
      }
    },
    query: async ({ model, prompt, stream = false }) => {
      const uri = 'http://127.0.0.1:11434/api/generate';
      model = model || "gemma3:1b"; // Specified model gemma3:1b deepseek-r1:8b
      if (!prompt) {
        console.error('modelQueries - ollama: Error no prompt', prompt);
        return null;
      }
      try {
        const response = await fetch(uri, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            model,
            prompt,
            stream, // We want a single response
          }),
        });
        if (!response.ok) {
          const errorData = await response.json();
          console.error('modelQueries Ollama query error:', errorData);
          return null;
        }
        const data = await response.json();
        const modelResponse = data.response;
        return modelResponse;
      } catch (error) {
        console.error('Error fetching metadata from Ollama:', error);
        return null;
      }
    },
  },
  google_gemini: {
    query: async ({ model, prompt, stream = false }) => { 
      // Initialize Google AI
      const modelName = model || 'gemini-2.5-flash-lite-preview-06-17'; // Specify the model you want to use
      const genAI = new GoogleGenerativeAI(process.env.GOOGLE_AI_API_KEY);
      const genAIModel = genAI.getGenerativeModel({ model: modelName });
      const result = await genAIModel.generateContent(prompt);
      const response = result.response.text().trim();
      return response;
    },
  },
  prompts: {
    metadataExtraction: async (queryText, inputLang = 'auto') => {
        return `
        You are a hotel information parser. Extract the following structured metadata from the given query text about a hotel or accommodation facility.

        FIELDS TO EXTRACT:
        1. **country**: The country where the hotel is located (e.g., Turkey, France). Only extract if explicitly mentioned.
        2. **region**: The region or area within the country (e.g., Aegean Region, Cappadocia). Only extract if explicitly mentioned.
        3. **city**: The city where the hotel is located (e.g., Istanbul, Antalya). Only extract if explicitly mentioned.
        3. **town**: The town or district within the city (e.g., Kadiköy, Belek). Only extract if explicitly mentioned.
        4. **hotelName**: The exact name of the hotel or facility as written in the text. Do NOT paraphrase or guess.
        5. **category**: The category or star rating of the hotel (e.g., (KATEGORI) VALUES
          ('5 Yıldızlı Otel'), ('4 Yıldızlı Otel'), ('Tatil Köyü'), ('3 Yıldızlı Otel'), ('(Yok)'), ('Özel Belgeli'), ('Hotel'),
          ('Otel'), ('Butik Otel'), ('2 Yıldızlı Otel'), ('1 Yıldızlı Hotel'), ('1 Yıldızlı Otel'), ('Motel'), ('Apart'), ('Oberj'), ('Pansiyon'), 
          ('2 Yıldızlı Hotel'), ('Dağ Evi'), ('4 Yıldızlı Hotel'), ('5 Yıldızlı Hotel'), ('3 Yıldızlı Hotel'), ('Kırsal Turizm Tesisi'), ('Villa'), ('Camping'), ('Hostel'), 
          ('Köy Evi'), ('1.Sınıf'), ('Plaj');
        ). Only extract if explicitly mentioned.

        INSTRUCTIONS:
        - Return only a valid JSON object with exactly these keys.
        - Do NOT add explanations, markdown, or extra text.
        - If any field cannot be determined from the text, set its value to null.
        - Use Turkish for values if the input text is in Turkish; otherwise, use English. Keys remain in English.
        - Never make up or infer hotel names. Only use what is explicitly stated in the text.
        - Be strict and only extract values that clearly match the criteria.

        TEXT TO ANALYZE:
        "${queryText}"
        `;
    },
  }
};

async function extractMetadataFromQuery({ queryText, debug = false }) {
  if (!queryText || typeof queryText !== 'string' || queryText.trim() === '') {
    console.error('Error: Invalid query text provided for metadata extraction.');
    return null;
  }
  const prompt = await modelQueries.prompts.metadataExtraction(queryText);
  debug && console.log('Metadata extraction prompt:', prompt);
  const modelFn = useLocalModel.forMetadataExtraction ? modelQueries.ollama.query : modelQueries.google_gemini.query;
  const modelResponse = await modelFn({ prompt });
  if (!modelResponse) {
    console.error('Error: No response from Model for metadata extraction.');
    return null;
  }
  try {
    const metadata = fnSupport.extractAndParseJSON(modelResponse);
    debug && console.log('Extracted Metadata:', JSON.stringify(metadata, null, 2));
    return metadata;
  } catch (jsonError) {
    console.error('Error parsing JSON from Model response:', jsonError);
    console.log('Raw Model response:', modelResponse);
    return null;
  }
};


// ChromaDB sonuçlarını işleyerek Gemini'ye özel bir prompt oluşturma ve JSON çıktısı alma fonksiyonu
async function findAndFormatHotelsForGemini(userQuery, chromaResults, geminiModel) {
  if (!chromaResults || !chromaResults.ids || !chromaResults.documents || !chromaResults.metadatas) {
    console.log("ChromaDB'den geçerli sonuçlar alınamadı.");
    return []; // Boş dizi döndür
  }

  const ids = chromaResults.ids[0] || [];
  const documents = chromaResults.documents[0] || [];
  const metadatas = chromaResults.metadatas[0] || [];

  // Kullanıcının arama sorgusunu prompt'un bir parçası olarak ekleyelim
  let promptText = `Kullanıcı şu otelleri arıyor: "${userQuery}".\n\n`;
  promptText += "Aşağıdaki otellerin bilgilerini kullanarak, kullanıcı sorgusuna en uygun olanları ve detaylarını JSON formatında listele.\n";
  promptText += "JSON formatı şu şekilde olmalı:\n";
  promptText += `
[
  {
    "TESIS_ID": "...",
    "TESIS_ADI": "...",
    "KATEGORI": "...",
    "BOLGE_ADI": "...",
    "ALT_BOLGE_ADI": "...",
    "BOLGE_DETAY": "...",
    "ACIKLAMA": "...",
    "KONAKLAMA_ACIKLAMA": "...",
    "KIMLER_KALMALI": "..."
  },
  ...
]
\n`;
  promptText += "ChromaDB'den gelen verilerle bu JSON'u doldur:\n\n";

  // Verileri eşleştirerek Gemini'nin anlayacağı bir formatta sunalım
  for (let i = 0; i < ids.length; i++) {
    const id = ids[i];
    const documentContent = documents[i] || "";
    const metadata = metadatas[i] || {};

    // 'hotel_' önekini temizle
    const tesIsId = id.replace("hotel_", "");

    // Belgeleri ayrıştırarak ilgili alanları çıkaralım
    let aciklama = "";
    let konaklamaAciklama = "";
    let kimlerKalmali = "";

    // Belge içeriğini basitçe ayrıştırıyoruz. Daha karmaşık parsing için regex veya başka yöntemler gerekebilir.
    const parts = documentContent.split(',');
    parts.forEach(part => {
      if (part.includes("aciklama:")) {
        aciklama = part.split("aciklama:")[1]?.trim() || "";
      } else if (part.includes("konaklama_aciklama:")) {
        konaklamaAciklama = part.split("konaklama_aciklama:")[1]?.trim() || "";
      } else if (part.includes("kimler_kalmali:")) {
        kimlerKalmali = part.split("kimler_kalmali:")[1]?.trim() || "";
      }
    });
    // Eğer belge formatı daha farklıysa, buradaki ayrıştırma mantığını güncellemeniz gerekebilir.
    // Örnek: 'tesis_adi: Hilton Ankara, konum: Ankara merkeze yakın, yıldız sayısı: 5'
    // Bu örnekte, 'aciklama', 'konaklama_aciklama', 'kimler_kalmali' doğrudan belge içinde bulunmuyor gibi görünüyor.
    // Eğer belge formatı "tesis_adi: ..., aciklama: ..., konaklama_aciklama: ..." şeklinde ise aşağıdaki gibi ayıklama yapılabilir:
    // Belgeyi parçalarına ayırıp ilgili anahtar-değer çiftlerini bulmak daha sağlam bir yaklaşım olacaktır.

    // Örnek: Eğer belge içeriği formatı şu şekilde ise:
    // "tesis_adi: Hilton Ankara, kategori: 5 Yıldız, bolge_adi: Ankara, alt_bolge_adi: Cankaya, bolge_detay: Sehir Merkezi, aciklama: Muhtesem manzara, konaklama_aciklama: Luks odalar, kimler_kalmali: Is Insanlari"
    // Bu durumda aşağıdaki gibi ayrıştırma daha iyi çalışır:

    const documentFields = {};
    const regex = /(\w+):\s*(.*?)(?=(?:, \w+:|$))/g;
    let match;
    while ((match = regex.exec(documentContent)) !== null) {
        documentFields[match[1]] = match[2];
    }

    // JSON'a eklemeden önce gerekli alanların var olduğundan emin olalım
    // Belge içeriğinden çıkarılamayanlar için metadata'dan veya varsayılan değerler kullanabiliriz.

    const hotelData = {
        TESIS_ID: tesIsId,
        TESIS_ADI: metadata.tesis_adi || documentFields.tesis_adi || "Bilinmiyor",
        KATEGORI: metadata.kategori || documentFields.kategori || "Bilinmiyor",
        BOLGE_ADI: metadata.bolge_adi || documentFields.bolge_adi || "Bilinmiyor",
        ALT_BOLGE_ADI: metadata.alt_bolge_adi || documentFields.alt_bolge_adi || "Bilinmiyor",
        BOLGE_DETAY: metadata.bolge_detay || documentFields.bolge_detay || "Bilinmiyor",
        // Bu alanlar documentContent'ten ayrıştırılmalı veya varsayılan değerler atanmalı.
        // Eğer belgelerinizde bu bilgiler doğrudan yer almıyorsa, burayı kendi veri yapınıza göre uyarlayın.
        ACIKLAMA: documentFields.aciklama || "Açıklama mevcut değil.",
        KONAKLAMA_ACIKLAMA: documentFields.konaklama_aciklama || "Konaklama açıklaması mevcut değil.",
        KIMLER_KALMALI: documentFields.kimler_kalmali || "Belirtilmemiş."
    };

    // Sadece gerçekten bir ID ve belge içeriği olanları ekleyelim
    if (hotelData.TESIS_ID && (hotelData.TESIS_ADI || documentFields.tesis_adi)) {
       promptText += `\nOtel: ${hotelData.TESIS_ADI} (ID: ${hotelData.TESIS_ID})\n`;
       promptText += `Kategori: ${hotelData.KATEGORI}, Bölge: ${hotelData.BOLGE_ADI}\n`;
       promptText += `Açıklama: ${hotelData.ACIKLAMA}\n`;
       promptText += `Konaklama Açıklaması: ${hotelData.KONAKLAMA_ACIKLAMA}\n`;
       promptText += `Kimler Kalmalı: ${hotelData.KIMLER_KALMALI}\n`;
       promptText += "---\n";
    }
  }

  // Eğer hiç uygun otel bulunamazsa kullanıcıya bilgi ver
  if (promptText.endsWith("ChromaDB'den gelen verilerle bu JSON'u doldur:\n\n")) {
      promptText += "Hiç otel bilgisi bulunamadı.\n";
  }


  // Gemini'ye JSON formatında yanıt vermesi için talimat
  promptText += `\nYukarıdaki bilgilere dayanarak, kullanıcının "${userQuery}" sorgusuna en uygun otelleri içeren bir JSON listesi oluştur.
  Tesis bilgilerinde kullanicinin sorgusuna göre değerlendirme yap ve kisa ve öz ir şekilde otel bilgilerine JSON içinde yorum anahtarıyla ekle
  JSON formatı aşağıdaki gibi olmalı:
  [
    {
      "TESIS_ID": "otel_kimliği",
      "TESIS_ADI": "otel adı",
      "KATEGORI": "otel kategorisi (örneğin, 5 Yıldız)",
      "BOLGE_ADI": "otel bölgesi",
      "ALT_BOLGE_ADI": "otel alt bölgesi",
      "BOLGE_DETAY": "otel bölgesi detayı",
      "ACIKLAMA": "otel genel açıklaması",
      "KONAKLAMA_ACIKLAMA": "otel konaklama açıklaması",
      "KIMLER_KALMALI": "otel kimler için uygun",
      "YORUM": "otel hakkında kısa yorum"
    }
  ]
  Eğer eşleşen otel yoksa, boş bir JSON dizisi [] döndür.`;

  console.log("--- Gemini'ye Gönderilecek Prompt ---");
  console.log(promptText);
  console.log("------------------------------------");

  try {
    const responseText = await geminiModel({prompt: promptText});
    
    console.log("\n--- Gemini Ham Yanıtı ---");
    console.log(responseText);
    console.log("------------------------");

    // Gemini'den gelen yanıtı JSON olarak ayrıştırmaya çalışalım
    try {
      // Bazen Gemini yanıtı markdown code block içinde dönebilir, bunu temizlemeye çalışalım
      const jsonMatch = responseText.match(/```json\n([\s\S]*?)\n```/);
      let jsonString = responseText;
      if (jsonMatch && jsonMatch[1]) {
        jsonString = jsonMatch[1];
      }
      // Boş JSON dizisi veya hata durumunu ele alalım
      if (jsonString.trim() === "" || jsonString.trim() === "[]") {
        console.log("Gemini'den eşleşen otel bilgisi dönmedi.");
        return [];
      }
      const formattedHotels = JSON.parse(jsonString);
      return formattedHotels;
    } catch (parseError) {
      console.error("Gemini yanıtını JSON olarak ayrıştırma hatası:", parseError);
      console.error("Ayrıştırılamayan yanıt:", responseText);
      return []; // Hata durumunda boş dizi döndür
    }

  } catch (error) {
    console.error("Gemini ile iletişimde hata oluştu:", error);
    return []; // Hata durumunda boş dizi döndür
  }
}


async function main() {
  const debug = true;
  const dtBOP = Date.now();
  try {
    const queryText = process.argv[2] || '5 yildizli kiriş oteli';
    let metadata = await extractMetadataFromQuery({queryText, debug: false});
    debug && console.log('Query:', queryText, metadata);
    let recs = await fnChroma.queryCollection({queryText, metadata, debug: false});
    // console.log('Records in collection:', JSON.stringify(recs, null, 2));
    
    const formattedHotels = await findAndFormatHotelsForGemini(queryText, recs, modelQueries.google_gemini.query);

    console.log("\n--- Sonuç (JSON Formatında) ---");
    console.log(JSON.stringify(formattedHotels, null, 2)); // JSON'u okunabilir şekilde yazdır
    console.log("------------------------------");


    console.log('Query completed in:', Date.now() - dtBOP, 'ms');
  } catch (error) {
    console.error('Fatal error initializing ChromaDB:', error);
    throw error;
  }
}

main();




// async function checkCollectionExists(collname = 'tours') {
//   try {
//     const collections = await client.listCollections();
//     console.log('Available collections:', await fnChroma.listCollections());
//     // Check if the collection with the given name exists
//     if (!collections || collections.length === 0) {
//       console.log('No collections found.');
//       return false; // No collections found
//     } else {
//       console.log('Collections found.');
//     }
//     return collections.some(coll => coll.name === collname);
//   } catch (error) {
//     console.error('Error checking collection existence:', error);
//     return false;
//   }
// }
// async function createColl(collname = 'noname') {
//   try {
//     const collection = await client.createCollection({
//       name: collname,
//     });
//     console.log(`Collection "${collname}" created successfully..`, collection);
//     return true;
//   } catch (error) {
//     // console.error('Error creating collection:', error);
//     return true;
//   }
// }

// async function addrec2coll(data, collname = 'tours') {
//   try {

//     const collection = await client.getCollection({ name: collname });
//     await collection.add(data);
//     console.log(`Records added to collection "${collname}" successfully..`, data);
//     return true;
//   } catch (error) {
//     console.error('Error adding to collection:', error);
//     return false;
//   }
// }




      // if (metadata.region) {
      //   metadataFilter.KATEGORI = { $like: `%${metadata.region}%` };
      // }

      // if (metadata.city) {
      //   metadataFilter.SEHIR = { $like: `%${metadata.city}%` };
      // }

      // if (metadata.country) {
      //   metadataFilter.BOLGE_DETAY = { $like: `%${metadata.country}%` };
      // }

      // if (metadata.category) {
      //   metadataFilter.KATEGORI = { $eq: `%${metadata.category}%` };
      // }



      // if (metadata.category) {
      //   metadataFilter.KATEGORI = { $like: `%${metadata.category}%` };
      // }

      //author: {"$in": ["Rowling", "Fitzgerald", "Herbert"]}
      // if (metadata.category) metadataFilter.KATEGORI = { "$in": ["5 yildizli otel"] };
      // // if (metadata.category) metadataFilter.KATEGORI = { "$eq": metadata.category };

      // if (Object.keys(metadataFilter).length > 0) {
      //   queryOptions.where = metadataFilter;
      // }