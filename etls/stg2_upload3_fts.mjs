import sqlite3 from 'sqlite3';
import { open } from 'sqlite';
import fs from 'fs';
import path from 'path';

async function main() {
    const dtBop = Date.now();

    try {
        const files2Import = [
            { name: 'q_tesis_listesi', fileName: 'data_tesis_listesi.json' },
            { name: 'q_ga4_stats', fileName: 'data_ga4_stats.json' },
            { name: 'q_salesPerItems', fileName: 'data_salesPerItems.json' },
        ];

        console.log('SQLite veritabanı oluşturuluyor...');

        // SQLite veritabanı yolunu belirle
        const dbPath = path.join(process.cwd(), 'data_hotel_local.db');
        const dbExists = fs.existsSync(dbPath);

        if (dbExists) {
            console.log(`✓ Mevcut SQLite veritabanı bulundu: ${dbPath}`);
        } else {
            // console.log(`✓ Yeni SQLite veritabanı oluşturuluyor: ${dbPath}`);
            console.error(`💥 SQLite veritabanı bulunamadi ${dbPath}`);
            process.exit(1);
        }

        // SQLite veritabanını aç/oluştur
        const db = await open({
            filename: dbPath,
            driver: sqlite3.Database
        });
        console.log(`📁 SQLite veritabanı: ${dbPath}`);
        console.log(`\n📊 q_summary sorgusu çalıştırılıyor...`);
        const summaryStartTime = Date.now();
 

        try {
            // 1. FTS4 tablosunu oluştur
            await db.run(`
            CREATE VIRTUAL TABLE IF NOT EXISTS q_summary_fts USING fts4(
                BOLGE_ADI TEXT,
                ALT_BOLGE_ADI TEXT,
                BOLGE_DETAY TEXT,
                KATEGORI TEXT,
                TESIS_ADI TEXT
            )
        `);
            console.log('FTS4 tablosu oluşturuldu veya zaten mevcut.');

            // 2. Mevcut verileri FTS tablosuna aktar
            await db.run(`
            INSERT OR IGNORE INTO q_summary_fts (docid, BOLGE_ADI, ALT_BOLGE_ADI, BOLGE_DETAY, KATEGORI, TESIS_ADI)
            SELECT rowid, BOLGE_ADI, ALT_BOLGE_ADI, BOLGE_DETAY, KATEGORI, TESIS_ADI FROM q_summary
        `);
            console.log('Mevcut veriler FTS tablosuna aktarıldı.');

            // 3. Yeni kayıt eklendiğinde FTS tablosuna yansıtacak TRIGGER
            await db.run(`
            CREATE TRIGGER IF NOT EXISTS after_q_summary_insert AFTER INSERT ON q_summary
            BEGIN
                INSERT INTO q_summary_fts (docid, BOLGE_ADI, ALT_BOLGE_ADI, BOLGE_DETAY, KATEGORI, TESIS_ADI)
                VALUES (new.rowid, new.BOLGE_ADI, new.ALT_BOLGE_ADI, new.BOLGE_DETAY, new.KATEGORI, new.TESIS_ADI);
            END;
        `);

            // 4. Kayıt güncellendiğinde FTS tablosunu güncelle
            await db.run(`
            CREATE TRIGGER IF NOT EXISTS after_q_summary_update AFTER UPDATE ON q_summary
            BEGIN
                UPDATE q_summary_fts
                SET 
                    BOLGE_ADI = new.BOLGE_ADI,
                    ALT_BOLGE_ADI = new.ALT_BOLGE_ADI,
                    BOLGE_DETAY = new.BOLGE_DETAY,
                    KATEGORI = new.KATEGORI,
                    TESIS_ADI = new.TESIS_ADI
                WHERE docid = new.rowid;
            END;
        `);

            // 5. Kayıt silindiğinde FTS'ten de sil
            await db.run(`
            CREATE TRIGGER IF NOT EXISTS after_q_summary_delete AFTER DELETE ON q_summary
            BEGIN
                DELETE FROM q_summary_fts WHERE docid = old.rowid;
            END;
        `);

            console.log('Tüm trigger\'lar başarıyla oluşturuldu.');
        } catch (error) {
            console.error('Bir hata oluştu:', error.message);
        } finally {
            await db.close();
            const finalElapsedTime = Date.now() - dtBop;
            console.log(`\n🎉 Tüm işlemler tamamlandı! Toplam süre: ${finalElapsedTime}ms (${(finalElapsedTime / 1000).toFixed(2)}s)`);
            console.log(`📁 SQLite veritabanı: ${dbPath}`); 
        }

        // Veritabanını kapat
        // await db.close();

    } catch (error) {
        console.error('💥 Genel hata oluştu:', error);
        process.exit(1);
    }
}

// Script'i çalıştır
main();


