#!/usr/bin/env python3
import chromadb
import requests

# ChromaDB client oluştur
client = chromadb.HttpClient(host='localhost', port=8000)

try:
    # Koleksiyon oluştur veya al
    collection = client.get_or_create_collection(
        name="hotel_collection",
        metadata={"hnsw:space": "cosine"}
    )
    print(f"✓ Koleksiyon başarıyla oluşturuldu: {collection.name}")
    print(f"✓ Koleksiyon ID: {collection.id}")
    
    # Test verisi ekle
    collection.add(
        documents=["Bu bir test oteli açıklamasıdır"],
        metadatas=[{"test": "true"}],
        ids=["test_1"]
    )
    print("✓ Test verisi eklendi")
    
    # Koleksiyon sayısını kontrol et
    count = collection.count()
    print(f"✓ Koleksiyonda {count} adet veri var")
    
except Exception as e:
    print(f"❌ Hata: {e}")

# API endpoint'lerini test et
print("\n--- API Endpoint Testleri ---")

# Heartbeat test
try:
    response = requests.get("http://localhost:8000/api/v1/heartbeat")
    print(f"v1 heartbeat: {response.status_code} - {response.text}")
except Exception as e:
    print(f"v1 heartbeat hatası: {e}")

# v2 heartbeat test
try:
    response = requests.get("http://localhost:8000/api/v2/heartbeat")
    print(f"v2 heartbeat: {response.status_code} - {response.text}")
except Exception as e:
    print(f"v2 heartbeat hatası: {e}")

# Collections endpoint test
try:
    response = requests.get("http://localhost:8000/api/v2/collections")
    print(f"v2 collections: {response.status_code} - {response.text[:200]}")
except Exception as e:
    print(f"v2 collections hatası: {e}")
