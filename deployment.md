# Sales Dashboard Deployment Guide

## Prerequisites

1. **Firebase Project Setup**
   - Create a Firebase project at https://console.firebase.google.com
   - Enable Authentication with Email/Password and Google providers
   - Enable Firestore Database
   - Generate a service account key and download the JSON file

2. **Google AI API Key**
   - Get an API key from Google AI Studio
   - Enable the Generative AI API

3. **Environment Variables**
   - Copy `.env.example` to `.env.local`
   - Fill in all required environment variables

## Local Development

1. **Install Dependencies**
   ```bash
   npm install
   ```

2. **Set up Environment Variables**
   ```bash
   cp .env.example .env.local
   # Edit .env.local with your actual values
   ```

3. **Run Development Server**
   ```bash
   npm run dev
   ```

4. **Run Tests**
   ```bash
   npm test
   ```

## Production Deployment

### Vercel Deployment (Recommended)

1. **Install Vercel CLI**
   ```bash
   npm i -g vercel
   ```

2. **Deploy to Vercel**
   ```bash
   vercel
   ```

3. **Set Environment Variables in Vercel**
   - Go to your project dashboard on Vercel
   - Navigate to Settings > Environment Variables
   - Add all variables from `.env.example`

4. **Configure Firebase Service Account**
   - Upload your service account JSON file to Vercel
   - Or set individual environment variables for Firebase Admin

### Alternative: Docker Deployment

1. **Create Dockerfile**
   ```dockerfile
   FROM node:18-alpine
   WORKDIR /app
   COPY package*.json ./
   RUN npm ci --only=production
   COPY . .
   RUN npm run build
   EXPOSE 3000
   CMD ["npm", "start"]
   ```

2. **Build and Run**
   ```bash
   docker build -t sales-dashboard .
   docker run -p 3000:3000 --env-file .env.local sales-dashboard
   ```

## Security Checklist

- [ ] All environment variables are set correctly
- [ ] Firebase security rules are configured
- [ ] CORS is properly configured
- [ ] Rate limiting is enabled
- [ ] HTTPS is enforced in production
- [ ] Service account key is securely stored
- [ ] Admin users are properly configured

## Firebase Security Rules

### Firestore Rules
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can only read/write their own user document
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      allow read: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    // Sales data access control
    match /salesData/{document} {
      allow read, write: if request.auth != null && 
        (resource.data.userId == request.auth.uid || 
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin');
      allow create: if request.auth != null && request.auth.uid == resource.data.userId;
    }
    
    // Invitations - admin only
    match /invitations/{document} {
      allow read, write: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    // Onboarding data
    match /onboarding/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
  }
}
```

### Authentication Rules
```javascript
// Firebase Auth configuration
{
  "users": {
    "allowedProviders": ["password", "google.com"],
    "allowedDomains": ["your-domain.com"], // Optional: restrict to specific domains
    "requireEmailVerification": true
  }
}
```

## Monitoring and Maintenance

1. **Set up Firebase Analytics**
   - Enable Analytics in Firebase Console
   - Monitor user engagement and app performance

2. **Error Monitoring**
   - Integrate with Sentry or similar service
   - Monitor API errors and performance

3. **Database Monitoring**
   - Set up Firestore usage alerts
   - Monitor read/write operations

4. **Regular Backups**
   - Set up automated Firestore backups
   - Test backup restoration procedures

## Performance Optimization

1. **Next.js Optimization**
   - Enable static generation where possible
   - Optimize images with Next.js Image component
   - Use dynamic imports for code splitting

2. **Database Optimization**
   - Create appropriate Firestore indexes
   - Implement pagination for large datasets
   - Use Firestore offline persistence

3. **Caching Strategy**
   - Implement Redis for session caching
   - Use CDN for static assets
   - Cache API responses where appropriate

## Troubleshooting

### Common Issues

1. **Authentication Errors**
   - Check Firebase configuration
   - Verify service account permissions
   - Ensure environment variables are correct

2. **API Rate Limiting**
   - Check rate limit configurations
   - Monitor API usage patterns
   - Implement proper error handling

3. **Database Permission Errors**
   - Review Firestore security rules
   - Check user roles and permissions
   - Verify document structure

### Debug Mode

Enable debug logging in development:
```javascript
// In your Firebase config
if (process.env.NODE_ENV === 'development') {
  firebase.firestore().enableNetwork();
  firebase.auth().useDeviceLanguage();
}
```

## Support and Documentation

- Firebase Documentation: https://firebase.google.com/docs
- Next.js Documentation: https://nextjs.org/docs
- Google AI Documentation: https://ai.google.dev/docs

For additional support, check the project's GitHub repository or contact the development team.
