#!/usr/bin/env node
// cli: echo '{"jsonrpc":"2.0","id":1,"method":"tools/call","params":{"name":"detailedTourList","arguments":{"criteria":"safari"}}}' | node tts-server.mjs

import fetch from 'node-fetch';
import { Server } from "@modelcontextprotocol/sdk/server/index.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
} from "@modelcontextprotocol/sdk/types.js";


const tourListURL = 'https://tourai.subanet.com/api/tours/list';
const tourDetailURL = 'https://tourai.subanet.com/api/tours/tour';
const getweatherURL = 'https://tourai.subanet.com/api/tools/getweather';
const searchwebURL = 'https://tourai.subanet.com/api/tools/searchweb';
const gettimeURL = 'https://tourai.subanet.com/api/tools/gettime';

async function getTourList(criteria, detailed = false) {
  let url = `${tourListURL}?criteria=${criteria}`;
  url += detailed ? '&detailed=true' : '';
  const response = await fetch(url);
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }
  const data = await response.json();
  return data.data.tours;
}

async function getTourDetail(tourId) {
  const url = `${tourDetailURL}/${tourId}`;
  const response = await fetch(url);
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }
  return await response.json();
}

async function getWeather(location) {
  const url = `${getweatherURL}?location=${location}`;
  const response = await fetch(url);
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }
  return await response.json();
}

async function searchWeb(query) {
  const url = `${searchwebURL}?query=${encodeURIComponent(query)}`;
  const response = await fetch(url);
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }
  return await response.json();
}

async function getTime(location) {
  const url = `${gettimeURL}?location=${location}`;
  const response = await fetch(url);
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }
  return await response.json();
}

class PingPongServer {
  constructor() {
    this.server = new Server(
      {
        name: "tts-mcp-server",
        version: "0.1.0",
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    this.setupToolHandlers();
  }

  setupToolHandlers() {
    // Handle list_tools requests
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: [
          {
            name: "ping",
            description: "Send a ping and receive a pong response",
            inputSchema: {
              type: "object",
              properties: {
                message: {
                  type: "string",
                  description: "Optional message to include with the ping",
                  default: "ping"
                }
              },
              additionalProperties: false,
            },
          },
          {
            name: "echo",
            description: "Echo back any message sent to it",
            inputSchema: {
              type: "object",
              properties: {
                text: {
                  type: "string",
                  description: "Text to echo back"
                }
              },
              required: ["text"],
              additionalProperties: false,
            },
          }, 
          {
            name: "listTours",
            description: "Get a list of tours filtered by criteria",
            inputSchema: {
              type: "object",
              properties: {
                criteria: {
                  type: "string",
                  description: "The criteria to filter the tours by (e.g., destination)"
                }
              },
              required: ["criteria"],
              additionalProperties: false,
            },
          },
          {
            name: "detailedTourList",
            description: "Get a detailed list of tours filtered by criteria",
            inputSchema: {
              type: "object",
              properties: {
                criteria: {
                  type: "string",
                  description: "The criteria to filter the tours by (e.g., destination)"
                }
              },
              required: ["criteria"],
              additionalProperties: false,
            },
          },
          {
            name: "getTourDetails",
            description: "Get detailed information about a specific tour",
            inputSchema: {
              type: "object",
              properties: {
                tourId: {
                  type: "string",
                  description: "The ID of the tour to retrieve"
                }
              },
              required: ["tourId"],
              additionalProperties: false,
            },
          },
          {
            name: "getWeather",
            description: "Get weather information for a specific location",
            inputSchema: {
              type: "object",
              properties: {
                location: {
                  type: "string",
                  description: "The location to get weather information for"
                }
              },
              required: ["location"],
              additionalProperties: false,
            },
          },
          {
            name: "searchWeb",
            description: "Search the web for a given query",
            inputSchema: {
              type: "object",
              properties: {
                query: {
                  type: "string",
                  description: "The search query"
                }
              },
              required: ["query"],
              additionalProperties: false,
            },
          },
          {
            name: "getTime",
            description: "Get current time information for a specific location",
            inputSchema: {
              type: "object",
              properties: {
                location: {
                  type: "string",
                  description: "The location to get time information for"
                }
              },
              required: ["location"],
              additionalProperties: false,
            },
          },
        ],
      };
    });

    // Handle call_tool requests
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;

      switch (name) {
        case "ping":
          const message = args.message || "ping";
          return {
            content: [
              {
                type: "text",
                text: `Received: "${message}" - Response: "pong!"`,
              },
            ],
          };

        case "listTours":
          if (!args.criteria) {
            throw new Error("Missing required parameter: criteria");
          }
          const tours = await getTourList(args.criteria);
          return {
            content: [
              {
                type: "text",
                text: JSON.stringify(tours, null, 2),
              },
            ],
          };

        case "detailedTourList":
          if (!args.criteria) {
            throw new Error("Missing required parameter: criteria");
          }
          const detailedTours = await getTourList(args.criteria, true);
          return {
            content: [
              {
                type: "text",
                text: JSON.stringify(detailedTours, null, 2),
              },
            ],
          };

        case "getTourDetails":
          if (!args.tourId) {
            throw new Error("Missing required parameter: tourId");
          }
          const details = await getTourDetail(args.tourId);
          return {
            content: [
              {
                type: "text",
                text: JSON.stringify(details, null, 2),
              },
            ],
          };

        case "echo":
          if (!args.text) {
            throw new Error("Missing required parameter: text");
          }
          return {
            content: [
              {
                type: "text",
                text: `Echo: ${args.text}`,
              },
            ],
          };

        case "getWeather":
          if (!args.location) {
            throw new Error("Missing required parameter: location");
          }
          const weather = await getWeather(args.location);
          return {
            content: [
              {
                type: "text",
                text: JSON.stringify(weather, null, 2),
              },
            ],
          };
          
        case "searchWeb":
          if (!args.query) {
            throw new Error("Missing required parameter: query");
          }
          const searchResult = await searchWeb(args.query);
          return {
            content: [
              {
                type: "text",
                text: JSON.stringify(searchResult, null, 2),
              },
            ],
          };

        case "getTime":
          if (!args.location) {
            throw new Error("Missing required parameter: location");
          }
          const time = await getTime(args.location);
          return {
            content: [
              {
                type: "text",
                text: JSON.stringify(time, null, 2),
              },
            ],
          };

        default:
          throw new Error(`Unknown tool: ${name}`);
      }
    });
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error("Tour MCP server running on stdio");
  }
}

// Start the server
const server = new PingPongServer();
server.run().catch(console.error);