// gemini-tool-client.js

import { GoogleGenerativeAI } from "@google/generative-ai";
import { spawn } from "child_process";
import readlineSync from "readline-sync";

const GEMINI_API_KEY = "AIzaSyDD0nBBLLrHe0mAkUN5yGL77LXqxcbkBEM"; // Kendi API key'inizi girin
const genAI = new GoogleGenerativeAI(GEMINI_API_KEY);

// MCP sunucusunu başlat ve iletişim kur
function startMcpServerProcess() {
  const serverProcess = spawn("node", ["tts-server.mjs"]);
  return {
    stdin: serverProcess.stdin,
    stdout: serverProcess.stdout,
    stderr: serverProcess.stderr,
    process: serverProcess
  };
}

async function sendToolCall(serverProcess, toolName, args = {}) {
  return new Promise(async (resolve, reject) => {
    const id = `req-${Math.random().toString(36).substring(7)}`;
    const request = {
      jsonrpc: "2.0",
      id: id,
      method: "tools/call",
      params: {
        name: toolName,
        arguments: args
      }
    };

    const reqStr = JSON.stringify(request);
    console.log("[CLIENT] Gönderilen tool_call:", reqStr);

    let responseBuffer = ""; // String buffer kullan

    const timeout = setTimeout(() => {
      reject(new Error("Zaman aşımı: tool_response gelmedi"));
    }, 5000);

    const onData = (data) => {
      try {
        responseBuffer += data.toString(); // Buffer'a ekle
        
        // Tam bir JSON yanıtı için kontrol et
        if (responseBuffer.includes('\n')) {
          const lines = responseBuffer.split('\n');
          for (const line of lines) {
            if (!line.trim()) continue;
            
            try {
              const response = JSON.parse(line);
              if (response.id === id) {
                clearTimeout(timeout);
                serverProcess.stdout.off("data", onData);
                resolve(response.result);
                return;
              }
            } catch (err) {
              // Eğer parse edilemezse, muhtemelen tam yanıt gelmemiştir
              continue;
            }
          }
          // İşlenen satırları buffer'dan temizle
          responseBuffer = lines[lines.length - 1];
        }
      } catch (err) {
        console.error("[CLIENT] Yanıt işlenirken hata:", err.message);
      }
    };

    serverProcess.stdout.on("data", onData);
    serverProcess.stdin.write(reqStr + "\n");

    // Cleanup
    setTimeout(() => {
      serverProcess.stdout.off("data", onData);
    }, 6000);
  });
}

// Ana fonksiyon
async function main() {
  const { stdin, stdout, stderr, process: serverProcess } = startMcpServerProcess();

  stderr.on("data", (data) => {
    console.error(`[SERVER-ERROR] ${data}`);
  });

  const model = genAI.getGenerativeModel({
    model: "gemini-2.5-flash-preview-05-20",
    tools: {
      functionDeclarations: [
        {
          name: "detailedTourList",
          description: "Kriterlere göre detaylı tur listesini döner",
          parameters: {
            type: "object",
            properties: {
              criteria: { type: "string" }
            },
            required: ["criteria"]
          }
        },
        {
          name: "listTours",
          description: "Kriterlere göre tur listesini döner",
          parameters: {
            type: "object",
            properties: {
              criteria: { type: "string" }
            },
            required: ["criteria"]
          }
        },
        {
          name: "getTourDetails",
          description: "Belirli bir turun detaylarını döner",
          parameters: {
            type: "object",
            properties: {
              tourId: { type: "string" }
            },
            required: ["tourId"]
          }
        },
        {
          name: "getWeather",
          description: "Belirli bir lokasyon için hava durumu bilgisini döner",
          parameters: {
            type: "object",
            properties: {
              location: { type: "string" }
            },
            required: ["location"]
          }
        },
        {
          name: "searchWeb",
          description: "Verilen sorgu için web araması yapar",
          parameters: {
            type: "object",
            properties: {
              query: { type: "string" }
            },
            required: ["query"]
          }
        },
        {
          name: "getTime",
          description: "Belirli bir lokasyon için güncel saat bilgisini döner",
          parameters: {
            type: "object",
            properties: {
              location: { type: "string" }
            },
            required: ["location"]
          }
        }
      ]
    }
  });

  const chat = model.startChat();

  while (true) {
    const userInput = readlineSync.question("\nSoru girin (Çıkmak için 'q'): ");
    if (userInput === "q") break;

    try {
      const result = await chat.sendMessage(userInput);
      const response = result.response;

      const toolCalls = response.candidates?.[0]?.content?.parts?.filter(
        part => part.hasOwnProperty("functionCall")
      );

      if (toolCalls && toolCalls.length > 0) {
        for (const toolCall of toolCalls) {
          const name = toolCall.functionCall.name;
          const args = toolCall.functionCall.args;

          let toolResult;
          if (name === "simple_ping") {
            toolResult = await sendToolCall(serverProcess, "ping");
          } else if (name === "get_tour_list") {
            toolResult = await sendToolCall(serverProcess, "detailedTourList", args);
          } 
          
          const toolResponsePart = {
            functionResponse: {
              name: name,
              response: toolResult
            }
          }; 

          const finalResult = await chat.sendMessage([toolResponsePart]);
          console.log("\n🧠 Gemini Cevabı:", finalResult.response.text());
        }
      } else {
        console.log("\n🧠 Gemini Cevabı:", response.text());
      }
    } catch (error) {
      console.error("❌ Hata oluştu:", error.message);
    }
  }

  serverProcess.kill();
  console.log("🔌 MCP sunucusu sonlandırıldı.");
}

main();