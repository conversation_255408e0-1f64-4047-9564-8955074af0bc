
//QUERY: Top 20% Ciro in month:
db.getCollection('tourai.data.fact.sales_hotelsdomestic').aggregate([
  // Adım 0: Sadece 2025-06 dönemine ait satışları filtrele
  {
    $match: {
      "SATIS_DONEM": "2025-06"
    }
  },
  // Adım 1: Her tesisin toplam gelirini hesapla
  {
    $group: {
      _id: "$TESIS_ID", // TESIS_ID'ye göre grupla
      tesisTotalRevenue: { $sum: "$tTutar" } // Her tesisin kendi toplam gelirini hesapla
    }
  },
  // Adım 2: Tüm tesislerin toplam gelirini ve %20 hedefini hesaplamak için tüm tesis gelirlerini topla
  {
    $group: {
      _id: null, // Tüm dokümanları tek bir grupta topla
      overallTotalRevenue: { $sum: "$tesisTotalRevenue" }, // Tüm tesislerin toplam gelirini hesapla
      tesisRevenues: {
        $push: {
          TESIS_ID: "$_id",
          tesisTotalRevenue: "$tesisTotalRevenue"
        }
      }
    }
  },
  // Adım 3: Toplam gelirin %20'sini hesapla ve tesis gelirlerini azalan sırada sırala
  {
    $addFields: {
      target20Percent: { $multiply: ["$overallTotalRevenue", 0.20] } // Toplam gelirin %20'sini hesapla
    }
  },
  {
    $unwind: "$tesisRevenues" // Tesis gelirleri dizisini aç
  },
  {
    $sort: {
      "tesisRevenues.tesisTotalRevenue": -1 // Her tesisin toplam gelirine göre azalan sırala
    }
  },
  // Adım 4: Kümülatif toplamı hesaplamak için $setWindowFields kullanma (MongoDB 5.0+ için)
  {
    $setWindowFields: {
      partitionBy: null, // Tek bir bölüm
      sortBy: { "tesisRevenues.tesisTotalRevenue": -1 },
      output: {
        cumulativeRevenue: {
          $sum: "$tesisRevenues.tesisTotalRevenue",
          window: { documents: ["unbounded", "current"] } // Mevcut dokümana kadar kümülatif toplam
        }
      }
    }
  },
  // Adım 5: %20 hedefine ulaşana kadar olan tesisleri filtrele
  {
    $match: {
      $expr: {
        // Bu koşul, bir önceki tesisin kümülatif geliri %20'nin altındaysa
        // VEYA bu tesisin eklenmesiyle %20'yi aşıyorsa bu tesisi dahil eder.
        // Amaç, %20'yi 'dolduran' tesisleri almaktır.
        $lte: ["$cumulativeRevenue", { $add: ["$target20Percent", "$tesisRevenues.tesisTotalRevenue"] }]
      }
    }
  },
  // Adım 6: Sadece ilgilendiğimiz alanları seç
  {
    $project: {
      _id: "$tesisRevenues.TESIS_ID",
      TESIS_ID: "$tesisRevenues.TESIS_ID",
      tesisTotalRevenue: "$tesisRevenues.tesisTotalRevenue",
      overallTotalRevenue: "$overallTotalRevenue",
      target20Percent: "$target20Percent",
      cumulativeRevenue: "$cumulativeRevenue"
    }
  },
  // Adım 7: Tesis bilgilerini birleştir
  {
    $lookup: {
      from: "tourai.data.dim.hotelsdomestic", // Tesis bilgileri koleksiyonu
      localField: "TESIS_ID", // Satış koleksiyonundan gelen TESIS_ID
      foreignField: "TESIS_ID", // Tesis koleksiyonundaki TESIS_ID
      as: "tesisDetay"
    }
  },
  {
    $unwind: "$tesisDetay" // Birleştirilen tesis detaylarını aç
  },
  // Adım 8: Sonuçları düzenle ve kaç tesis olduğunu hesapla
  {
    $group: {
      _id: null,
      overallTotalRevenue: { $first: "$overallTotalRevenue" },
      target20Percent: { $first: "$target20Percent" },
      cumulativeRevenueReached: { $max: "$cumulativeRevenue" }, // %20'ye ulaşan kümülatif toplamı (bu, son tesisle birlikte aşılmış olabilir)
      selectedTesislerCount: { $sum: 1 }, // Kaç tesis olduğunu say
      selectedTesisler: {
        $push: {
          TESIS_ID: "$TESIS_ID",
          TESIS_ADI: "$tesisDetay.TESIS_ADI",
          KATEGORI: "$tesisDetay.KATEGORI",
          BOLGE_ADI: "$tesisDetay.BOLGE_ADI",
          tesisGeliri: "$tesisTotalRevenue"
        }
      }
    }
  },
  {
    $project: {
      _id: 0,
      overallTotalRevenue: 1,
      target20Percent: 1,
      cumulativeRevenueReached: 1,
      selectedTesislerCount: 1, // Kaç tesis olduğunu göster
      selectedTesisler: 1
    }
  }
]);

// QUERY: Top 10 Ciro:
db.getCollection('tourai.data.fact.sales_hotelsdomestic').aggregate([
  // 1. 2025-06 dönemine göre filtreleme
  {
    $match: {
      SATIS_DONEM: "2025-06"
    }
  },

  // 2. TESIS_ID'ye göre gruplama ve toplam ciro hesabı
  {
    $group: {
      _id: "$TESIS_ID",
      toplamCiro: { $sum: "$tTutar" }
    }
  },

  // 3. En yüksek cirolardan başlayarak sıralama
  {
    $sort: {
      toplamCiro: -1
    }
  },

  // 4. İlk 10 kaydı al
  {
    $limit: 10
  },

  // 5. Tesis isimlerini getirmek için tesisler koleksiyonu ile eşleştirme
  {
    $lookup: {
      from: "tourai.data.dim.hotelsdomestic",
      localField: "_id",
      foreignField: "TESIS_ID",
      as: "tesis_bilgisi"
    }
  },

  // 6. Eşleşen tesis bilgisinden gerekli alanları çıkarma
  {
    $unwind: "$tesis_bilgisi"
  },

  // 7. Son çıktıda sadece gerekli alanları göster
  {
    $project: {
      _id: 0,
      TESIS_ID: "$_id",
      TESIS_ADI: "$tesis_bilgisi.TESIS_ADI",
      BOLGE_ADI: "$tesis_bilgisi.BOLGE_ADI",
      ALT_BOLGE_ADI: "$tesis_bilgisi.ALT_BOLGE_ADI",
      toplamCiro: 1
    }
  }
])
  
// QUERY : 2025 Haziran döneminde, 2025 Nisan dönemine göre cirosu en çok düşen ve WEBSITE_LISTED değeri "TRUE" olan ilk 10 tesisi listeleyen MongoDB sorgusu aşağıdadır
db.getCollection('tourai.data.fact.sales_hotelsdomestic').aggregate([
  // Adım 1: WEBSITE_LISTED değeri TRUE olan ve 2025-05 veya 2025-06 dönemine ait satışları filtrele
  {
    $match: {
      "SATIS_DONEM": { $in: ["2025-05", "2025-06"] }
    }
  },
  // Adım 2: Her tesis için dönem bazında toplam ciroyu hesapla
  {
    $group: {
      _id: {
        TESIS_ID: "$TESIS_ID",
        SATIS_DONEM: "$SATIS_DONEM"
      },
      donemCiro: { $sum: "$tTutar" }
    }
  },
  // Adım 3: Tesisleri ve dönem cirolarını yeniden yapılandır
  {
    $group: {
      _id: "$_id.TESIS_ID",
      donemler: {
        $push: {
          SATIS_DONEM: "$_id.SATIS_DONEM",
          donemCiro: "$donemCiro"
        }
      }
    }
  },
  // Adım 4: 2025-05 ve 2025-06 cirolarını ayrı alanlara ayır
  {
    $addFields: {
      ciro202505: { // Mayıs cirosu
        $filter: {
          input: "$donemler",
          as: "d",
          cond: { $eq: ["$$d.SATIS_DONEM", "2025-05"] }
        }
      },
      ciro202506: { // Haziran cirosu
        $filter: {
          input: "$donemler",
          as: "d",
          cond: { $eq: ["$$d.SATIS_DONEM", "2025-06"] }
        }
      }
    }
  },
  // Adım 5: Ciro değerlerini diziden tek bir değere dönüştür ve ciro düşüşünü hesapla
  {
    $addFields: {
      ciroMayis: { $arrayElemAt: ["$ciro202505.donemCiro", 0] },
      ciroHaziran: { $arrayElemAt: ["$ciro202506.donemCiro", 0] }
    }
  },
  // Sadece her iki dönemde de cirosu olan tesisleri ele al
  {
    $match: {
      ciroMayis: { $exists: true, $ne: null, $gt: 0 }, // Nisan cirosunun 0'dan büyük olması, yüzde hesaplamasında sıfıra bölme hatasını önler
      ciroHaziran: { $exists: true, $ne: null }
    }
  },
  {
    $addFields: {
      ciroDususu: { $subtract: ["$ciroMayis", "$ciroHaziran"] } // Mayıs'tan Haziran'ı çıkararak düşüşü bul
    }
  },
  // Adım 6: Sadece ciro düşüşü olan tesisleri filtrele
  {
    $match: {
      ciroDususu: { $gt: 0 } // Cirosu düşmüş olanları filtrele (Mayıs > Haziran)
    }
  },
  // Adım 7: Yüzde düşüşünü hesapla
  {
    $addFields: {
      ciroDususuYuzde: {
        $cond: {
          if: { $gt: ["$ciroMayis", 0] }, // Nisan cirosu sıfırdan büyükse hesapla
          then: { $multiply: [{ $divide: ["$ciroDususu", "$ciroMayis"] }, 100] },
          else: 0 // Aksi takdirde 0 olarak ayarla (veya null, duruma göre)
        }
      }
    }
  },
  // Adım 8: Ciro düşüşüne göre büyükten küçüğe sırala (yüzde düşüşüne göre sıralamak isterseniz buradan değiştirebilirsiniz)
  {
    $sort: {
      ciroDususu: -1 // Eğer yüzde düşüşüne göre sıralamak isterseniz: ciroDususuYuzde: -1 yapın
    }
  },
  // Adım 9: İlk 10 tesisi al
  {
    $limit: 10
  },
  // Adım 10: Tesis bilgilerini birleştir ve WEBSITE_LISTED değerini kontrol et
  {
    $lookup: {
      from: "tourai.data.dim.hotelsdomestic", // Tesis bilgileri koleksiyonu
      localField: "_id", // Satış koleksiyonundan gelen TESIS_ID
      foreignField: "TESIS_ID", // Tesis koleksiyonundaki TESIS_ID
      as: "tesisDetay"
    }
  },
  {
    $unwind: "$tesisDetay" // Birleştirilen tesis detaylarını aç
  },
  // Adım 11: WEBSITE_LISTED değeri "TRUE" olanları filtrele
  {
    $match: {
      "tesisDetay.WEBSITE_LISTED": "TRUE"
    }
  },
  // Adım 12: Sonuçları düzenle
  {
    $project: {
      _id: 0,
      TESIS_ID: "$_id",
      TESIS_ADI: "$tesisDetay.TESIS_ADI",
      BOLGE_ADI: "$tesisDetay.BOLGE_ADI",
      ciroMayis2025: "$ciroMayis",
      ciroHaziran2025: "$ciroHaziran",
      ciroDususu: "$ciroDususu",
      ciroDususuYuzde: { $round: ["$ciroDususuYuzde", 2] }, // Yüzdeyi iki ondalık basamağa yuvarla
      WEBSITE_LISTED: "$tesisDetay.WEBSITE_LISTED"
    }
  }
])

// QUERY : Ciro husus yuzdesi en yüksek 10 tesis.
db.getCollection('tourai.data.fact.sales_hotelsdomestic').aggregate([
  // Adım 1: Sadece 2025-05 veya 2025-06 dönemine ait satışları filtrele
  {
    $match: {
      "SATIS_DONEM": { $in: ["2025-05", "2025-06"] }
    }
  },
  // Adım 2: Her tesis için dönem bazında toplam ciroyu hesapla
  {
    $group: {
      _id: {
        TESIS_ID: "$TESIS_ID",
        SATIS_DONEM: "$SATIS_DONEM"
      },
      donemCiro: { $sum: "$tTutar" }
    }
  },
  // Adım 3: Tesisleri ve dönem cirolarını yeniden yapılandır
  {
    $group: {
      _id: "$_id.TESIS_ID",
      donemler: {
        $push: {
          SATIS_DONEM: "$_id.SATIS_DONEM",
          donemCiro: "$donemCiro"
        }
      }
    }
  },
  // Adım 4: 2025-05 ve 2025-06 cirolarını ayrı alanlara ayır
  {
    $addFields: {
      ciro202505: { // Mayıs cirosu
        $filter: {
          input: "$donemler",
          as: "d",
          cond: { $eq: ["$$d.SATIS_DONEM", "2025-05"] }
        }
      },
      ciro202506: { // Haziran cirosu
        $filter: {
          input: "$donemler",
          as: "d",
          cond: { $eq: ["$$d.SATIS_DONEM", "2025-06"] }
        }
      }
    }
  },
  // Adım 5: Ciro değerlerini diziden tek bir değere dönüştür ve ciro düşüşünü hesapla
  {
    $addFields: {
      ciroMayis: { $arrayElemAt: ["$ciro202505.donemCiro", 0] },
      ciroHaziran: { $arrayElemAt: ["$ciro202506.donemCiro", 0] }
    }
  },
  // Sadece her iki dönemde de cirosu olan ve Mayıs cirosu 0'dan büyük olan tesisleri ele al
  {
    $match: {
      ciroMayis: { $exists: true, $ne: null, $gt: 0 }, // Mayıs cirosu 0'dan büyük olmalı (yüzde hesaplaması için)
      ciroHaziran: { $exists: true, $ne: null }
    }
  },
  {
    $addFields: {
      ciroDususu: { $subtract: ["$ciroMayis", "$ciroHaziran"] } // Mayıs'tan Haziran'ı çıkararak düşüşü bul
    }
  },
  // Adım 6: Sadece ciro düşüşü olan tesisleri filtrele (Mayıs > Haziran)
  {
    $match: {
      ciroDususu: { $gt: 0 } // Cirosu düşmüş olanları filtrele
    }
  },
  // Adım 7: Yüzde düşüşünü hesapla
  {
    $addFields: {
      ciroDususuYuzde: {
        $cond: {
          if: { $gt: ["$ciroMayis", 0] }, // Mayıs cirosu sıfırdan büyükse hesapla
          then: { $multiply: [{ $divide: ["$ciroDususu", "$ciroMayis"] }, 100] },
          else: 0 // Aksi takdirde 0 olarak ayarla
        }
      }
    }
  },
  // Adım 8: Ciro düşüş yüzdesine göre büyükten küçüğe sırala
  {
    $sort: {
      ciroDususuYuzde: -1 // Yüzde düşüşüne göre azalan sıralama
    }
  },
  // Adım 9: İlk 10 tesisi al
  {
    $limit: 10
  },
  // Adım 10: Tesis bilgilerini birleştir
  {
    $lookup: {
      from: "tourai.data.dim.hotelsdomestic", // Tesis bilgileri koleksiyonu
      localField: "_id", // Satış koleksiyonundan gelen TESIS_ID
      foreignField: "TESIS_ID", // Tesis koleksiyonundaki TESIS_ID
      as: "tesisDetay"
    }
  },
  {
    $unwind: "$tesisDetay" // Birleştirilen tesis detaylarını aç
  },
  // Adım 11: WEBSITE_LISTED değeri "TRUE" olanları filtrele
  {
    $match: {
      "tesisDetay.WEBSITE_LISTED": "TRUE"
    }
  },
  // Adım 12: Sonuçları düzenle
  {
    $project: {
      _id: 0,
      TESIS_ID: "$_id",
      TESIS_ADI: "$tesisDetay.TESIS_ADI",
      BOLGE_ADI: "$tesisDetay.BOLGE_ADI",
      ciroMayis2025: "$ciroMayis",
      ciroHaziran2025: "$ciroHaziran",
      ciroDususu: "$ciroDususu",
      ciroDususuYuzde: { $round: ["$ciroDususuYuzde", 2] }, // Yüzdeyi iki ondalık basamağa yuvarla
      WEBSITE_LISTED: "$tesisDetay.WEBSITE_LISTED"
    }
  }
])
 
// QUERY : Tesis düşüşlerinin yuzdesel gruplanması.
db.getCollection('tourai.data.fact.sales_hotelsdomestic').aggregate([
  // Adım 1: Sadece 2025-05 veya 2025-06 dönemine ait satışları filtrele
  {
    $match: {
      "SATIS_DONEM": { $in: ["2025-05", "2025-06"] }
    }
  },
  // Adım 2: Her tesis için dönem bazında toplam ciroyu hesapla
  {
    $group: {
      _id: {
        TESIS_ID: "$TESIS_ID",
        SATIS_DONEM: "$SATIS_DONEM"
      },
      donemCiro: { $sum: "$tTutar" }
    }
  },
  // Adım 3: Tesisleri ve dönem cirolarını yeniden yapılandır
  {
    $group: {
      _id: "$_id.TESIS_ID",
      donemler: {
        $push: {
          SATIS_DONEM: "$_id.SATIS_DONEM",
          donemCiro: "$donemCiro"
        }
      }
    }
  },
  // Adım 4: 2025-05 ve 2025-06 cirolarını ayrı alanlara ayır
  {
    $addFields: {
      ciro202505: {
        $filter: {
          input: "$donemler",
          as: "d",
          cond: { $eq: ["$$d.SATIS_DONEM", "2025-05"] }
        }
      },
      ciro202506: {
        $filter: {
          input: "$donemler",
          as: "d",
          cond: { $eq: ["$$d.SATIS_DONEM", "2025-06"] }
        }
      }
    }
  },
  // Adım 5: Ciro değerlerini diziden tek bir değere dönüştür ve ciro düşüşünü hesapla
  {
    $addFields: {
      ciroMayis: { $arrayElemAt: ["$ciro202505.donemCiro", 0] },
      ciroHaziran: { $arrayElemAt: ["$ciro202506.donemCiro", 0] }
    }
  },
  // Sadece her iki dönemde de cirosu olan ve Mayıs cirosu 0'dan büyük olan tesisleri ele al
  {
    $match: {
      ciroMayis: { $exists: true, $ne: null, $gt: 0 },
      ciroHaziran: { $exists: true, $ne: null }
    }
  },
  {
    $addFields: {
      ciroDususu: { $subtract: ["$ciroMayis", "$ciroHaziran"] }
    }
  },
  // Adım 6: Sadece ciro düşüşü olan tesisleri filtrele (Mayıs > Haziran)
  {
    $match: {
      ciroDususu: { $gt: 0 }
    }
  },
  // Adım 7: Yüzde düşüşünü hesapla
  {
    $addFields: {
      ciroDususuYuzde: {
        $cond: {
          if: { $gt: ["$ciroMayis", 0] },
          then: { $multiply: [{ $divide: ["$ciroDususu", "$ciroMayis"] }, 100] },
          else: 0
        }
      }
    }
  },
  // Adım 8: WEBSITE_LISTED değerini kontrol etmek için tesis bilgilerini birleştir
  {
    $lookup: {
      from: "tourai.data.dim.hotelsdomestic", // Tesis bilgileri koleksiyonu
      localField: "_id", // Geçici olarak TESIS_ID'yi kullan
      foreignField: "TESIS_ID", // Tesis koleksiyonundaki TESIS_ID
      as: "tesisDetay"
    }
  },
  {
    $unwind: "$tesisDetay" // Birleştirilen tesis detaylarını aç
  },
  // Adım 9: WEBSITE_LISTED değeri "TRUE" olanları filtrele
  {
    $match: {
      "tesisDetay.WEBSITE_LISTED": "TRUE"
    }
  },
  // Adım 10: Yüzde düşüş oranına göre gruplama (buckets)
  {
    $bucket: {
      groupBy: "$ciroDususuYuzde", // Gruplama yapılacak alan
      boundaries: [0, 20, 50, 80, 100, Infinity], // Aralıklar
      default: "Diğer (100% üzeri veya negatif)", // Aralık dışında kalanlar için varsayılan bucket
      output: {
        tesisAdedi: { $sum: 1 }, // Her aralıktaki tesis sayısını say
        // İsteğe bağlı: Her aralıktaki tesislerin listesi
        // tesisler: { $push: { id: "$_id", ad: "$tesisDetay.TESIS_ADI", yuzdeDususu: "$ciroDususuYuzde" } }
      }
    }
  },
  // Adım 11: Sonuçları düzenle ve toplam tesis sayısını hesapla
  {
    $group: {
      _id: null, // Tüm grupları tek bir sonuçta toplamak için
      gruplar: {
        $push: {
          aralik: "$_id",
          tesisAdedi: "$tesisAdedi"
        }
      },
      toplamDususYasayanTesis: { $sum: "$tesisAdedi" } // Toplam düşüş yaşayan tesis sayısı
    }
  },
  // Adım 12: Son çıktıyı düzenle
  {
    $project: {
      _id: 0,
      gruplar: "$gruplar",
      toplamDususYasayanTesis: "$toplamDususYasayanTesis"
    }
  }
])

// QUERY : 2025-05 te satis gelen, ancak 2025-06 da website listed degeri true oldugu halde satis gelmeyen tesisler hangileri, 2025-05 teki cirolari en yuksek olandan asagiya dogru sirala. ilk 50 sini sırala. kalanını digeri de ve tesis adedini ve toplamını ekle ve en alta genel ciro toplamını ekle mayıs icin
db.getCollection('tourai.data.fact.sales_hotelsdomestic').aggregate([
  // Adım 1: Sadece 2025-05 ve 2025-06 dönemlerine ait satışları filtrele
  {
    $match: {
      "SATIS_DONEM": { $in: ["2025-05", "2025-06"] }
    }
  },
  // Adım 2: Her tesis için hangi dönemlerde satış geldiğini ve o dönemin cirosunu belirle
  {
    $group: {
      _id: "$TESIS_ID",
      mayisCiro: {
        $sum: {
          $cond: [{ $eq: ["$SATIS_DONEM", "2025-05"] }, "$tTutar", 0]
        }
      },
      haziranCiro: {
        $sum: {
          $cond: [{ $eq: ["$SATIS_DONEM", "2025-06"] }, "$tTutar", 0]
        }
      },
      satisVarMayis: {
        $sum: {
          $cond: [{ $eq: ["$SATIS_DONEM", "2025-05"] }, 1, 0]
        }
      },
      satisVarHaziran: {
        $sum: {
          $cond: [{ $eq: ["$SATIS_DONEM", "2025-06"] }, 1, 0]
        }
      }
    }
  },
  // Adım 3: 2025-05'te satışı olan (cirosu > 0) ve 2025-06'da hiç satışı olmayan (cirosu = 0) tesisleri filtrele
  {
    $match: {
      satisVarMayis: { $gt: 0 }, // 2025-05'te en az bir satış olmalı
      mayisCiro: { $gt: 0 }, // 2025-05 cirosu 0'dan büyük olmalı
      satisVarHaziran: { $eq: 0 }, // 2025-06'da hiç satış olmamalı
      haziranCiro: { $eq: 0 } // 2025-06 cirosu 0 olmalı
    }
  },
  // Adım 4: Tesis detaylarını (özellikle WEBSITE_LISTED) almak için lookup yap
  {
    $lookup: {
      from: "tourai.data.dim.hotelsdomestic",
      localField: "_id",
      foreignField: "TESIS_ID",
      as: "tesisDetay"
    }
  },
  // Adım 5: Tesis detaylarını unwind yap ve WEBSITE_LISTED değeri "TRUE" olanları filtrele
  {
    $unwind: "$tesisDetay"
  },
  {
    $match: {
      "tesisDetay.WEBSITE_LISTED": "TRUE"
    }
  },
  // Adım 6: 2025-05 cirosuna göre büyükten küçüğe sırala
  {
    $sort: {
      mayisCiro: -1
    }
  },
  // Adım 7: İlk 50'yi ve geri kalanı işlemek için $facet kullanma
  {
    $facet: {
      top50Tesisler: [
        { $limit: 50 },
        {
          $project: {
            _id: 0,
            TESIS_ID: "$_id",
            TESIS_ADI: "$tesisDetay.TESIS_ADI",
            BOLGE_ADI: "$tesisDetay.BOLGE_ADI",
            KATEGORI: "$tesisDetay.KATEGORI",
            WEBSITE_LISTED: "$tesisDetay.WEBSITE_LISTED",
            mayisCiro: "$mayisCiro",
            haziranCiro: "$haziranCiro"
          }
        }
      ],
      digerTesislerOzeti: [
        { $skip: 50 }, // İlk 50'yi atla
        {
          $group: {
            _id: null,
            adet: { $sum: 1 },
            toplamMayisCiro: { $sum: "$mayisCiro" }
          }
        },
        {
          $project: {
            _id: 0,
            aralik: "Diğer Tesisler",
            adet: "$adet",
            toplamMayisCiro: "$toplamMayisCiro"
          }
        }
      ],
      genelMayisCiroToplami: [
        {
          $group: {
            _id: null,
            toplamMayisCiro: { $sum: "$mayisCiro" }
          }
        },
        {
          $project: {
            _id: 0,
            genelToplamMayisCiro: "$toplamMayisCiro"
          }
        }
      ]
    }
  }
])

// QUERY : 2025 Mayıs ayında hiç satışı olmayan, ancak 2025 Haziran ayında satışı olan ve WEBSITE_LISTED: TRUE olan tesisleri listeleyeceğiz. Ayrıca, yine 2025 Haziran'daki cirolarına göre sıralayıp ilk 50'yi detaylı, kalanını özet olarak göstereceğiz ve toplam Haziran cirosunu ekleyeceğiz.
db.getCollection('tourai.data.fact.sales_hotelsdomestic').aggregate([
  // Adım 1: 2025-05 ve 2025-06 dönemlerine ait satışları filtrele
  {
    $match: {
      "SATIS_DONEM": { $in: ["2025-05", "2025-06"] }
    }
  },
  // Adım 2: Her tesis için hangi dönemlerde satış geldiğini ve o dönemin cirosunu belirle
  {
    $group: {
      _id: "$TESIS_ID",
      mayisCiro: {
        $sum: {
          $cond: [{ $eq: ["$SATIS_DONEM", "2025-05"] }, "$tTutar", 0]
        }
      },
      haziranCiro: {
        $sum: {
          $cond: [{ $eq: ["$SATIS_DONEM", "2025-06"] }, "$tTutar", 0]
        }
      },
      satisVarMayis: {
        $sum: {
          $cond: [{ $eq: ["$SATIS_DONEM", "2025-05"] }, 1, 0]
        }
      },
      satisVarHaziran: {
        $sum: {
          $cond: [{ $eq: ["$SATIS_DONEM", "2025-06"] }, 1, 0]
        }
      }
    }
  },
  // Adım 3: 2025-05'te hiç satışı olmayan (cirosu = 0) ve 2025-06'da satışı olan (cirosu > 0) tesisleri filtrele
  {
    $match: {
      satisVarMayis: { $eq: 0 }, // 2025-05'te hiç satış olmamalı
      mayisCiro: { $eq: 0 }, // 2025-05 cirosu 0 olmalı
      satisVarHaziran: { $gt: 0 }, // 2025-06'da en az bir satış olmalı
      haziranCiro: { $gt: 0 } // 2025-06 cirosu 0'dan büyük olmalı
    }
  },
  // Adım 4: Tesis detaylarını (özellikle WEBSITE_LISTED) almak için lookup yap
  {
    $lookup: {
      from: "tourai.data.dim.hotelsdomestic",
      localField: "_id",
      foreignField: "TESIS_ID",
      as: "tesisDetay"
    }
  },
  // Adım 5: Tesis detaylarını unwind yap ve WEBSITE_LISTED değeri "TRUE" olanları filtrele
  {
    $unwind: "$tesisDetay"
  },
  {
    $match: {
      "tesisDetay.WEBSITE_LISTED": "TRUE"
    }
  },
  // Adım 6: 2025-06 cirosuna göre büyükten küçüğe sırala
  {
    $sort: {
      haziranCiro: -1
    }
  },
  // Adım 7: İlk 50'yi ve geri kalanı işlemek için $facet kullanma
  {
    $facet: {
      top50Tesisler: [
        { $limit: 50 },
        {
          $project: {
            _id: 0,
            TESIS_ID: "$_id",
            TESIS_ADI: "$tesisDetay.TESIS_ADI",
            BOLGE_ADI: "$tesisDetay.BOLGE_ADI",
            KATEGORI: "$tesisDetay.KATEGORI",
            WEBSITE_LISTED: "$tesisDetay.WEBSITE_LISTED",
            mayisCiro: "$mayisCiro", // Bu durumda 0 olacaktır
            haziranCiro: "$haziranCiro"
          }
        }
      ],
      digerTesislerOzeti: [
        { $skip: 50 }, // İlk 50'yi atla
        {
          $group: {
            _id: null,
            adet: { $sum: 1 },
            toplamHaziranCiro: { $sum: "$haziranCiro" } // Haziran cirosunu özetle
          }
        },
        {
          $project: {
            _id: 0,
            aralik: "Diğer Tesisler",
            adet: "$adet",
            toplamHaziranCiro: "$toplamHaziranCiro"
          }
        }
      ],
      genelHaziranCiroToplami: [ // Genel Haziran Ciro Toplamı
        {
          $group: {
            _id: null,
            toplamHaziranCiro: { $sum: "$haziranCiro" }
          }
        },
        {
          $project: {
            _id: 0,
            genelToplamHaziranCiro: "$toplamHaziranCiro"
          }
        }
      ]
    }
  }
])
 
// QUERY : bu senaryo için 2025 yılı boyunca WEBSITE_LISTED değeri "TRUE" olan ancak hiç satış kaydı bulunmayan tesisler
db.getCollection('tourai.data.dim.hotelsdomestic').aggregate([
  // Adım 1: WEBSITE_LISTED değeri "TRUE" olan tüm tesisleri filtrele
  {
    $match: {
      "WEBSITE_LISTED": "TRUE"
    }
  },
  // Adım 2: 2025 yılında satış yapmış tesislerin TESIS_ID'lerini bul
  // (sales_hotelsdomestic koleksiyonundan 2025 yılına ait satışları topla)
  {
    $lookup: {
      from: "tourai.data.fact.sales_hotelsdomestic",
      localField: "TESIS_ID",
      foreignField: "TESIS_ID",
      as: "salesData"
    }
  },
  // Adım 3: Sadece 2025 yılına ait satışları filtrele (eğer varsa)
  {
    $addFields: {
      sales2025: {
        $filter: {
          input: "$salesData",
          as: "sale",
          cond: { $regexMatch: { input: "$$sale.SATIS_DONEM", regex: "^2025-" } }
        }
      }
    }
  },
  // Adım 4: 2025 yılında hiç satış gelmemiş tesisleri filtrele
  // (yani sales2025 dizisi boş olanları)
  {
    $match: {
      "sales2025": { $eq: [] } // 2025 yılına ait satış verisi olmayanları seç
    }
  },
  // Adım 5: Sonuçları düzenle
  {
    $project: {
      _id: 0,
      TESIS_ID: "$TESIS_ID",
      TESIS_ADI: "$TESIS_ADI",
      KATEGORI: "$KATEGORI",
      BOLGE_ADI: "$BOLGE_ADI",
      WEBSITE_LISTED: "$WEBSITE_LISTED",
      // sales2025 alanını göstermeye gerek yok, boş olduğunu biliyoruz
    }
  }
])

// QUERY : 2025 yılında listelenmiş ancak hiç satış gelmemiş tesislerin toplam sayısını ve bu tesislerin kategorilerine göre gruplandırılmış adetlerini bul
db.getCollection('tourai.data.dim.hotelsdomestic').aggregate([
  // Adım 1: WEBSITE_LISTED değeri "TRUE" olan tüm tesisleri filtrele
  {
    $match: {
      "WEBSITE_LISTED": "TRUE"
    }
  },
  // Adım 2: 2025 yılında satış yapmış tesislerin TESIS_ID'lerini bulmak için salesData'yı bağla
  {
    $lookup: {
      from: "tourai.data.fact.sales_hotelsdomestic",
      localField: "TESIS_ID",
      foreignField: "TESIS_ID",
      as: "salesData"
    }
  },
  // Adım 3: Sadece 2025 yılına ait satışları filtrele (eğer varsa)
  {
    $addFields: {
      sales2025: {
        $filter: {
          input: "$salesData",
          as: "sale",
          cond: { $regexMatch: { input: "$$sale.SATIS_DONEM", regex: "^2025-" } }
        }
      }
    }
  },
  // Adım 4: 2025 yılında hiç satış gelmemiş tesisleri filtrele (sales2025 dizisi boş olanları)
  {
    $match: {
      "sales2025": { $eq: [] }
    }
  },
  // Adım 5: Kategorilerine göre grupla ve her kategorideki tesis adedini say
  {
    $group: {
      _id: "$KATEGORI", // KATEGORI alanına göre grupla
      tesisAdedi: { $sum: 1 } // Her gruptaki tesis sayısını topla
    }
  },
  // Adım 6: Toplam tesis sayısını hesaplamak için yeni bir group aşaması
  {
    $group: {
      _id: null, // Tüm grupları tek bir sonuçta toplamak için
      kategoriBazindaTesisler: {
        $push: {
          kategori: "$_id",
          adet: "$tesisAdedi"
        }
      },
      toplamTesisAdedi: { $sum: "$tesisAdedi" } // Tüm kategorilerin toplam tesis adedini al
    }
  },
  // Adım 7: Sonuç çıktısını düzenle
  {
    $project: {
      _id: 0,
      toplamTesisAdedi: "$toplamTesisAdedi",
      kategoriBazindaTesisler: "$kategoriBazindaTesisler"
    }
  }
])


// QUERY: ciro dusmus olanlarin listesi

db.getCollection('tourai.data.fact.sales_hotelsdomestic').aggregate([
  // Adım 1: Sadece 2025-05 veya 2025-06 dönemine ait satışları filtrele
  {
    $match: {
      "SATIS_DONEM": { $in: ["2025-05", "2025-06"] }
    }
  },
  // Adım 2: Her tesis için dönem bazında toplam ciroyu hesapla
  {
    $group: {
      _id: "$TESIS_ID",
      mayisCiro: {
        $sum: {
          $cond: [{ $eq: ["$SATIS_DONEM", "2025-05"] }, "$tTutar", 0]
        }
      },
      haziranCiro: {
        $sum: {
          $cond: [{ $eq: ["$SATIS_DONEM", "2025-06"] }, "$tTutar", 0]
        }
      },
      satisVarMayis: {
        $sum: {
          $cond: [{ $eq: ["$SATIS_DONEM", "2025-05"] }, 1, 0]
        }
      },
      satisVarHaziran: {
        $sum: {
          $cond: [{ $eq: ["$SATIS_DONEM", "2025-06"] }, 1, 0]
        }
      }
    }
  },
  // Adım 3: Sadece her iki dönemde de cirosu olan ve Mayıs cirosu 0'dan büyük olan tesisleri ele al
  // NOT: Eğer "satisVarHaziran" 0 ise, "haziranCiro" da 0 olmalı, bu yüzden ayrıca kontrol etmeye gerek yok.
  // Ancak mantıksal tutarlılık için $match'te her ikisi de bırakılabilir.
  {
    $match: {
      mayisCiro: { $exists: true, $ne: null, $gt: 0 }, // Mayıs cirosu 0'dan büyük olmalı
      haziranCiro: { $exists: true, $ne: null }
    }
  },
  // Adım 4: Ciro düşüşünü hesapla
  {
    $addFields: {
      ciroDususu: { $subtract: ["$mayisCiro", "$haziranCiro"] }
    }
  },
  // Adım 5: Sadece ciro düşüşü olan tesisleri filtrele (Mayıs > Haziran)
  {
    $match: {
      ciroDususu: { $gt: 0 } // Cirosu düşmüş olanları filtrele
    }
  },
  // Adım 6: Yüzde düşüşünü hesapla
  {
    $addFields: {
      ciroDususuYuzde: {
        $cond: {
          if: { $gt: ["$mayisCiro", 0] },
          then: { $multiply: [{ $divide: ["$ciroDususu", "$mayisCiro"] }, 100] },
          else: 0
        }
      }
    }
  },
  // Adım 7: Tesis bilgilerini birleştir
  {
    $lookup: {
      from: "tourai.data.dim.hotelsdomestic", // Tesis bilgileri koleksiyonu
      localField: "_id", // TESIS_ID
      foreignField: "TESIS_ID", // Tesis koleksiyonundaki TESIS_ID
      as: "tesisDetay"
    }
  },
  {
    $unwind: "$tesisDetay" // Birleştirilen tesis detaylarını aç
  },
  // Adım 8: WEBSITE_LISTED değeri "TRUE" olanları filtrele
  {
    $match: {
      "tesisDetay.WEBSITE_LISTED": "TRUE"
    }
  },
  // Adım 9: Ciro düşüş oranının bulunduğu aralığı belirle (yeni kolon)
  {
    $addFields: {
      ciroDususuAraligi: {
        $cond: {
          if: { $and: [{ $gte: ["$ciroDususuYuzde", 0] }, { $lt: ["$ciroDususuYuzde", 20] }] },
          then: "0-20%",
          else: {
            $cond: {
              if: { $and: [{ $gte: ["$ciroDususuYuzde", 20] }, { $lt: ["$ciroDususuYuzde", 50] }] },
              then: "20-50%",
              else: {
                $cond: {
                  if: { $and: [{ $gte: ["$ciroDususuYuzde", 50] }, { $lt: ["$ciroDususuYuzde", 80] }] },
                  then: "50-80%",
                  else: {
                    $cond: {
                      if: { $and: [{ $gte: ["$ciroDususuYuzde", 80] }, { $lte: ["$ciroDususuYuzde", 100] }] },
                      then: "80-100%",
                      else: "100% Üzeri" // 100'den büyük düşüşler
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  },
  // Adım 10: Sonuçları Ciro Düşüş Yüzdesine göre sırala (opsiyonel)
  {
    $sort: {
      ciroDususuYuzde: -1 // En yüksek düşüş oranına sahip tesisler en üstte
    }
  },
  // Adım 11: İstenen kolonları seç ve düzenle
  {
    $project: {
      _id: 0,
      TESIS_ID: "$_id",
      TESIS_ADI: "$tesisDetay.TESIS_ADI",
      BOLGE_ADI: "$tesisDetay.BOLGE_ADI",
      KATEGORI: "$tesisDetay.KATEGORI",
      WEBSITE_LISTED: "$tesisDetay.WEBSITE_LISTED",
      ciroMayis2025: "$mayisCiro",
      ciroHaziran2025: "$haziranCiro",
      ciroDususuTL: { $round: ["$ciroDususu", 2] }, // Mutlak düşüş TL cinsinden
      ciroDususuYuzde: { $round: ["$ciroDususuYuzde", 2] }, // Yüzde düşüşü
      ciroDususuAraligi: "$ciroDususuAraligi" // Yeni eklenen aralık kolonu
    }
  }
]).toArray()



//2025 te satis gelmeyen tesis listesi

  db.getCollection('tourai.data.dim.hotelsdomestic').aggregate([
  {
    $match: {
      "WEBSITE_LISTED": 'TRUE'
    }
  },
  // Adım 1: 2025 yılında satış yapmış tüm benzersiz TESIS_ID'lerini sales koleksiyonundan al
  {
    $lookup: {
      from: "tourai.data.fact.sales_hotelsdomestic",
      localField: "TESIS_ID",
      foreignField: "TESIS_ID",
      pipeline: [
        {
          $match: {
            "SATIS_DONEM": { $regex: "^2025-" }
          }
        },
        {
          $group: {
            _id: "$TESIS_ID" // 2025'te satış yapmış benzersiz TESIS_ID'leri
          }
        }
      ],
      as: "salesIn2025"
    }
  },
  // Adım 2: salesIn2025 dizisi boş olanları filtrele
  // Yani 2025'te hiç satış yapmamış tesisler
  {
    $match: {
      "salesIn2025": { $eq: [] }
    }
  },
  // Adım 3: Sonuçları düzenle
  {
    $project: {
      _id: 0,
      TESIS_ID: "$TESIS_ID",
      TESIS_ADI: "$TESIS_ADI",
      KATEGORI: "$KATEGORI",
      BOLGE_ADI: "$BOLGE_ADI",
      WEBSITE_LISTED: "$WEBSITE_LISTED" // Bu kolonu da görebiliriz, filtrelemeye gerek yok
    }
  }
]);



//22025 top hotels
db.getCollection('tourai.data.fact.sales_hotelsdomestic').aggregate([
  // Adım 1: Sadece 2025 yılına ait satış kayıtlarını filtrele
  {
    $match: {
      "SATIS_DONEM": { $regex: "^2025-" }
    }
  },
  // Adım 2: TESIS_ID'ye göre grupla ve toplam rezervasyon adedi ile toplam ciroyu hesapla
  {
    $group: {
      _id: "$TESIS_ID", // TESIS_ID'ye göre grupla
      toplamRezvAdedi: { $sum: "$REZV" }, // Her tesis için toplam rezervasyon adedi
      toplamCiro: { $sum: "$tTutar" } // Her tesis için toplam ciro
    }
  },
  // Adım 3: Tesis detaylarını (Adı, Bölgesi vb.) almak için lookup yap
  {
    $lookup: {
      from: "tourai.data.dim.hotelsdomestic", // Tesis bilgileri koleksiyonu
      localField: "_id", // Satış koleksiyonundan gelen TESIS_ID (group sonrası _id)
      foreignField: "TESIS_ID", // Tesis koleksiyonundaki TESIS_ID
      as: "tesisDetay"
    }
  },
  // Adım 4: Tesis detaylarını unwind yap (her tesis için tek bir detay belgesi olmalı)
  // Eğer her TESIS_ID için dim.hotelsdomestic'te tek bir kayıt olduğundan eminseniz bu aşama güvenlidir.
  {
    $unwind: "$tesisDetay"
  },
  // Adım 5: Sonuçları düzenle, Tesis Adı ve Bölge bilgisini ekle
  {
    $project: {
      _id: 0,
      TESIS_ID: "$_id",
      TESIS_ADI: "$tesisDetay.TESIS_ADI", // Tesis adını ekle
      BOLGE_ADI: "$tesisDetay.BOLGE_ADI", // Bölge adını ekle
      toplamRezvAdedi: 1,
      toplamCiro: 1
    }
  },
  // Adım 6 (İsteğe bağlı): Cirosu yüksek olandan düşüğe sırala
  {
    $sort: {
      toplamCiro: -1
    }
  }
]);
