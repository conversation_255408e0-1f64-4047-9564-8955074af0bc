/* Copyright © 2023 Subanet Limited / Subanet.com
 *
 * All Rights Reserved.
 *
 * This software is protected by copyright law and is the 
 * property of Subanet Limited. Unauthorized use, reproduction 
 * or distribution of this software, or any portion thereof, 
 * is strictly prohibited.
 *
 */


import { NextRequest, NextResponse } from 'next/server'
import type { IncomingMessage } from "http";
import { JwtPayload, verify } from 'jsonwebtoken';
import { jwtVerify, type JWTPayload, decodeJwt } from 'jose';

export const hasMappedHeaders = (headers: Headers | IncomingMessage['headers']): headers is Headers => {
  return headers instanceof Headers;
};

export const config = {
  matcher: '/api/:function*',
}

export async function getIdentityToken(req: Request | IncomingMessage): Promise<JwtPayload> {
  const jwt = hasMappedHeaders(req.headers)
    ? req.headers.get('authorization')
    : req.headers['authorization'];
  return { jwt }
}

export async function middleware(request: NextRequest) {
  var isAuthenticated = (rxx) => {
    return new Promise(async (resolve) => {
      const nextUrl = rxx.nextUrl
      const nextHeaders = await getIdentityToken(rxx)
      const bearerTokenStg = nextHeaders ? nextHeaders.jwt : null;
      const bearerToken = bearerTokenStg ? bearerTokenStg.split(" ") : [];
      const token = bearerToken[1];
      var result = {
        isValid: false,
        payload: null,
        token: token,
      };
      var pathType = nextUrl.pathname.startsWith('/api/') ? (nextUrl.pathname.startsWith('/api/auth/') || nextUrl.pathname.startsWith('/api/pub/')) ? 'auth' : 'api' : 'web'
      var tokenCheckNeeded = pathType == 'api' ? true : (false)
      if (tokenCheckNeeded) {
        const key = process.env.JWT_KEY;
        try {
          if (token) {
            var tokenData = token && await verifytoken(token, key)
            if (tokenData && tokenData.isValid) {
              result = {
                isValid: true,
                payload: tokenData.payload,
                token: token,
              }; //true;
            } else {  
              console.log('token', token, tokenData)
              var decodedJWT = decodeJwt(token);
              // console.log('Decoded token:', {
              //   token: token,
              //   userId: decodedJWT.id,
              //   email: decodedJWT.email,
              //   customerId: decodedJWT.customerId,
              //   clientSchema: decodedJWT.clientSchema, // Eklendi
              //   exp: new Date(decodedJWT.exp * 1000).toLocaleString()
              // });
              const rawResponse = await fetch(nextUrl.origin + "/api/auth/refresh-token", {
                method: 'POST',
                headers: {
                  'Accept': 'application/json',
                  'Content-Type': 'application/json'
                },
                body: JSON.stringify({ payload: decodedJWT })
              });
              const content = await rawResponse.json();
              var refreshToken = content?.token;
              var tokenDataRefresh = await verifytoken(refreshToken, key)
              if (tokenDataRefresh && tokenDataRefresh.isValid) {
                result = {
                  isValid: true,
                  payload: tokenDataRefresh.payload,
                  token: refreshToken,
                };
              } else {
                result = {
                  isValid: false,
                  payload: null,
                  token: token,
                };
              }
            }
          } else {
            console.log('token verifiy err - no token')
            result = {
              isValid: false,
              payload: null,
              token: token,
            };
          }
        } catch (e) {
          console.log('token verifiy err', nextUrl, e)
          result = {
            isValid: false,
            payload: null,
            token: token,
          };
        }
      } else {
        result = {
          isValid: true,
          payload: null,
          token: token,
        };
      }
      resolve(result)
    });
  };

  var soOK = await isAuthenticated(request);
  if (!soOK || !soOK?.isValid) {
    console.log('authentication failed')
    return new NextResponse(
      JSON.stringify({ success: false, message: 'authentication failed' }),
      { status: 401, headers: { 'content-type': 'application/json' } }
    )
  }

  const requestHeaders = new Headers(request.headers)
  requestHeaders.set('token', soOK?.token)

  const response = NextResponse.next({
    request: {
      headers: requestHeaders,
    },
  })
  return response
}

export async function verifytoken(token: string, secret: string): Promise<JWTPayload> {
  var resp = {
    isValid: false,
    payload: null,
  }
  try {
    const { payload } = await jwtVerify(token, new TextEncoder().encode(secret));
    if (payload) {
      resp.payload = payload;
      if ((Date.now() - payload?.exp * 1000) < 0) {
        resp.isValid = true;
        return resp;
      }
    }
    return resp;
  } catch (e) {

    return resp;
  }
}
