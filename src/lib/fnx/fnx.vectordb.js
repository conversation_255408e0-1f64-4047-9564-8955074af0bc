import { GoogleGenerativeAI } from '@google/generative-ai';
import { pipeline, env } from '@xenova/transformers';

// WebAssembly için wasm yollarını ayarlayalım
env.backends.onnx.wasm.wasmPaths = 'https://onnxruntime.github.io/onnx-js-demo/dist/ ';

export const vectorEmbeddings = {
    google: async ({
        text,
        genAIpack,
        model = "text-embedding-004",
    }) => {
        if (!text) {
            console.error('google: Error no text');
            return null
        };
        try {
            // API anahtarı ile Google AI istemcisini başlat
            const GEMINI_API_KEY = process.env.GEMINI_API_KEY_YODA;
            const genAI = genAIpack || new GoogleGenerativeAI(GEMINI_API_KEY);
            // text-embedding-004 modelini al
            const embeddingModel = genAI.getGenerativeModel({ model });
            // Embedding oluştur
            const result = await embeddingModel.embedContent(text);
            // Embedding değerlerini çıkar
            if (result && result.embedding && Array.isArray(result.embedding.values)) {
                return result.embedding.values;
            } else {
                console.error('Unexpected response format from Google AI:', result);
                return null;
            }
        } catch (error) {
            console.error('Error generating embedding with Google AI:', error);
            return null;
        }
    },
    ollama: async ({
        text,
        uri = 'http://127.0.0.1:11434/api/embeddings',
        model = "mxbai-embed-large:latest",
    }) => {
        if (!text) {
            console.error('ollama: Error no text', text);
            return null
        };

        try {
            const response = await fetch(uri, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    model, prompt: text,
                }),
            });

            if (!response.ok) {
                const errorData = await response.json();
                console.error('Ollama embedding error:', errorData);
                return null;
            }

            const data = await response.json();
            return data.embedding;
        } catch (error) {
            console.error('Error generating embedding with Ollama:', error);
            return null;
        }
    },
    bertTurkish: async ({ text }) => {
        if (!text) {
            console.error('bertTurkish: Error no text', text);
            return null;
        }

        try {
            // Model zaten cached olabilir; tekrar yüklememek için singleton yapısı da kullanılabilir
            const extractor = await pipeline('feature-extraction', 'dbmdz/bert-base-turkish-cased');

            // Embedding çıkarma işlemi (normalize ve mean pooling ile)
            const output = await extractor(text, {
                pooling: 'mean',
                normalize: true
            });

            // Float32Array -> Array<number> çevir
            return Array.from(output.data);
        } catch (error) {
            console.error('Error generating Turkish BERT embedding:', error);
            return null;
        }
    }
}

export const dbtools = {
    copyCollection: async ({
        sourceDbName = 'source', sourceDb, targetDbName = 'target', targetDb, sourceCollection, targetCollection
    }) => {
        try {
            // 1. Kaynak koleksiyondan tüm dökümanları çek
            console.log(`🔄 "${sourceDbName}.tours" koleksiyonundan dökümanlar alınıyor...`);
            const cursor = sourceCollection.find({});
            const documents = await cursor.toArray();

            if (documents.length === 0) {
                console.log("⚠️ Kopyalanacak döküman bulunamadı.");
                return;
            }

            // 2. Hedef koleksiyona ekle
            console.log(`📥 "${targetDbName}.tours" koleksiyonuna ${documents.length} döküman ekleniyor...`);
            await targetCollection.insertMany(documents);
            console.log("✅ Kopyalama işlemi tamamlandı.");
        } catch (error) {
            console.error("❌ Hata oluştu:", error.message);
        } finally {
            console.log("🔌 Bağlantı kapatıldı.");
        }
    },
    copyCollectionInBatches: async ({
        sourceDbName = 'source',
        targetDbName = 'target',
        sourceCollection,
        targetCollection,
        batchSize = 250,

    }) => {

        try {
            // Get total document count
            const totalDocs = await sourceCollection.countDocuments();
            console.log(`🔍 Toplam ${totalDocs} döküman bulundu`);

            if (totalDocs === 0) {
                console.log("⚠️ Kopyalanacak döküman bulunamadı.");
                return;
            }

            let processedCount = 0;
            let batchNumber = 1;

            // Process documents in batches
            while (processedCount < totalDocs) {
                console.log(`🔄 Batch ${batchNumber}: İşleniyor...`);

                const documents = await sourceCollection.find({})
                    .skip(processedCount)
                    .limit(batchSize)
                    .toArray();

                if (documents.length === 0) break;

                // Insert batch into target collection
                await targetCollection.insertMany(documents);

                processedCount += documents.length;
                batchNumber++;

                console.log(`✅ İlerleme: ${processedCount}/${totalDocs} döküman (${Math.round(processedCount / totalDocs * 100)}%)`);
            }

            console.log("✅ Kopyalama işlemi tamamlandı.");
        } catch (error) {
            console.error("❌ Hata oluştu:", error.message);
            throw error;
        }
    },
    clientConn: async ({
        clientPromise
    }) => {
        // const client = await MongoClient.connect(MONGODB_URI);
        let client = await clientPromise;
        try {
            // Bağlantı durumunu kontrol et
            await client.db().admin().ping();
        } catch (error) {
            console.log("MongoDB bağlantısı kopmuş, yeniden bağlanılıyor...");
            client = await clientPromise;
        }
        return client;
    },
}