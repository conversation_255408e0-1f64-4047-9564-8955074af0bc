import { GoogleGenerativeAI } from '@google/generative-ai';

// Initialize the Gemini AI client
const genAI = new GoogleGenerativeAI(process.env.GOOGLE_AI_API_KEY);

// Get the Gemini Pro model
const model = genAI.getGenerativeModel({ model: 'gemini-2.5-flash-preview-05-20' });

export class GeminiAI {
  static async generateSalesInsights(salesData, customerData, timeframe = '30d') {
    try {
      const prompt = `As a sales analytics expert, analyze the following sales data and provide actionable insights:

        Sales Data:
        - Total Revenue: $${salesData.totalValue || 0}
        - Total Deals: ${salesData.totalDeals || 0}
        - Won Deals: ${salesData.wonDeals || 0}
        - Lost Deals: ${salesData.lostDeals || 0}
        - Average Deal Size: $${salesData.avgDealSize || 0}
        - Win Rate: ${salesData.winRate || 0}%
        - Conversion Rate: ${salesData.conversionRate || 0}%

        Customer Data:
        - Total Customers: ${customerData.totalCustomers || 0}
        - Active Customers: ${customerData.activeCustomers || 0}
        - Prospects: ${customerData.prospects || 0}

        Time Period: ${timeframe}

        Please provide:
        1. 3-5 key insights about sales performance
        2. 2-3 specific recommendations for improvement
        3. Potential risks or opportunities to watch
        4. Suggested next actions for the sales team

        Format your response as a JSON object with the following structure:
        {
          "insights": ["insight1", "insight2", ...],
          "recommendations": ["rec1", "rec2", ...],
          "risks_opportunities": ["item1", "item2", ...],
          "next_actions": ["action1", "action2", ...]
        }
      `;

      // Remove non-ASCII characters and trim
      let asciiPrompt = prompt.trim();
      asciiPrompt = asciiPrompt.normalize('NFKD').replace(/[^\x00-\x7F]/g, '');
      asciiPrompt = asciiPrompt.replace(/[\u2018\u2019\u201C\u201D]/g, "'");
      asciiPrompt = asciiPrompt.replace(/[\u2013\u2014]/g, '-');

      const result = await model.generateContent(asciiPrompt);
      const response = await result.response;
      const text = response.text();

      // Try to parse JSON response
      try {
        return JSON.parse(text);
      } catch (parseError) {
        // If JSON parsing fails, return a structured fallback
        return {
          insights: [text.substring(0, 200) + '...'],
          recommendations: ['Review the AI analysis for detailed recommendations'],
          risks_opportunities: ['Monitor key metrics closely'],
          next_actions: ['Implement suggested improvements']
        };
      }
    } catch (error) {
      console.error('Error generating sales insights:', error);
      return {
        insights: ['Unable to generate insights at this time'],
        recommendations: ['Please try again later'],
        risks_opportunities: ['Monitor performance manually'],
        next_actions: ['Check AI service status']
      };
    }
  }

  static async generateCustomerInsights(customerData) {
    try {
      const prompt = `
        Analyze the following customer data and provide insights for customer relationship management:

        Customer Overview:
        - Total Customers: ${customerData.totalCustomers || 0}
        - Active Customers: ${customerData.activeCustomers || 0}
        - Prospects: ${customerData.prospects || 0}
        - Customer Status Distribution: ${JSON.stringify(customerData)}

        Provide insights about:
        1. Customer health and engagement
        2. Opportunities for customer growth
        3. Risk factors for customer churn
        4. Recommended customer outreach strategies

        Format as JSON:
        {
          "customer_health": "assessment",
          "growth_opportunities": ["opp1", "opp2"],
          "churn_risks": ["risk1", "risk2"],
          "outreach_strategies": ["strategy1", "strategy2"]
        }
      `;

      // Remove non-ASCII characters and trim
      let asciiPrompt = prompt.trim();
      asciiPrompt = asciiPrompt.normalize('NFKD').replace(/[^\x00-\x7F]/g, '');
      asciiPrompt = asciiPrompt.replace(/[\u2018\u2019\u201C\u201D]/g, "'");
      asciiPrompt = asciiPrompt.replace(/[\u2013\u2014]/g, '-');

      const result = await model.generateContent(asciiPrompt);
      const response = await result.response;
      const text = response.text();

      try {
        return JSON.parse(text);
      } catch (parseError) {
        return {
          customer_health: 'Analysis in progress',
          growth_opportunities: ['Expand customer engagement programs'],
          churn_risks: ['Monitor inactive customers'],
          outreach_strategies: ['Implement regular check-ins']
        };
      }
    } catch (error) {
      console.error('Error generating customer insights:', error);
      return {
        customer_health: 'Unable to assess at this time',
        growth_opportunities: ['Manual review recommended'],
        churn_risks: ['Monitor key metrics'],
        outreach_strategies: ['Standard outreach protocols']
      };
    }
  }

  static async generateDealRecommendations(dealData) {
    try {
      const prompt = `
        Analyze this sales deal and provide recommendations:

        Deal Information:
        - Title: ${dealData.title}
        - Stage: ${dealData.stage}
        - Value: $${dealData.value}
        - Probability: ${dealData.probability}%
        - Days in current stage: ${dealData.daysInStage || 'Unknown'}
        - Customer: ${dealData.customer?.name || 'Unknown'}
        - Expected close date: ${dealData.expectedCloseDate || 'Not set'}

        Provide specific recommendations for:
        1. Moving this deal forward
        2. Increasing win probability
        3. Potential risks to address
        4. Next best actions

        Format as JSON:
        {
          "move_forward": ["action1", "action2"],
          "increase_probability": ["method1", "method2"],
          "risks": ["risk1", "risk2"],
          "next_actions": ["action1", "action2"]
        }
      `;

      // Remove non-ASCII characters and trim
      let asciiPrompt = prompt.trim();
      asciiPrompt = asciiPrompt.normalize('NFKD').replace(/[^\x00-\x7F]/g, '');
      asciiPrompt = asciiPrompt.replace(/[\u2018\u2019\u201C\u201D]/g, "'");
      asciiPrompt = asciiPrompt.replace(/[\u2013\u2014]/g, '-');

      const result = await model.generateContent(asciiPrompt);
      const response = await result.response;
      const text = response.text();

      try {
        return JSON.parse(text);
      } catch (parseError) {
        return {
          move_forward: ['Schedule follow-up meeting', 'Send proposal'],
          increase_probability: ['Address customer concerns', 'Provide case studies'],
          risks: ['Competition', 'Budget constraints'],
          next_actions: ['Contact customer', 'Update deal status']
        };
      }
    } catch (error) {
      console.error('Error generating deal recommendations:', error);
      return {
        move_forward: ['Standard follow-up procedures'],
        increase_probability: ['Review deal requirements'],
        risks: ['Monitor deal progress'],
        next_actions: ['Manual review recommended']
      };
    }
  }

  static async generateMarketingInsights(salesData, customerData) {
    try {
      const prompt = `
        Based on sales and customer data, provide marketing insights:

        Sales Performance:
        - Revenue: $${salesData.totalValue || 0}
        - Deals: ${salesData.totalDeals || 0}
        - Win Rate: ${salesData.winRate || 0}%

        Customer Base:
        - Total: ${customerData.totalCustomers || 0}
        - Active: ${customerData.activeCustomers || 0}
        - Prospects: ${customerData.prospects || 0}

        Provide marketing recommendations for:
        1. Lead generation strategies
        2. Customer retention tactics
        3. Market opportunities
        4. Campaign optimization

        Format as JSON:
        {
          "lead_generation": ["strategy1", "strategy2"],
          "retention_tactics": ["tactic1", "tactic2"],
          "market_opportunities": ["opp1", "opp2"],
          "campaign_optimization": ["opt1", "opt2"]
        }
      `;

      // Remove non-ASCII characters and trim
      let asciiPrompt = prompt.trim();
      asciiPrompt = asciiPrompt.normalize('NFKD').replace(/[^\x00-\x7F]/g, '');
      asciiPrompt = asciiPrompt.replace(/[\u2018\u2019\u201C\u201D]/g, "'");
      asciiPrompt = asciiPrompt.replace(/[\u2013\u2014]/g, '-');

      const result = await model.generateContent(asciiPrompt);
      const response = await result.response;
      const text = response.text();

      try {
        return JSON.parse(text);
      } catch (parseError) {
        return {
          lead_generation: ['Content marketing', 'Social media campaigns'],
          retention_tactics: ['Customer success programs', 'Regular check-ins'],
          market_opportunities: ['New market segments', 'Product expansion'],
          campaign_optimization: ['A/B testing', 'Performance tracking']
        };
      }
    } catch (error) {
      console.error('Error generating marketing insights:', error);
      return {
        lead_generation: ['Review current strategies'],
        retention_tactics: ['Implement best practices'],
        market_opportunities: ['Market research needed'],
        campaign_optimization: ['Performance analysis required']
      };
    }
  }

  static async generateForecast(historicalData, timeframe = '3m') {
    try {
      const prompt = `
        Based on historical sales data, generate a forecast for the next ${timeframe}:

        Historical Data:
        ${JSON.stringify(historicalData, null, 2)}

        Provide:
        1. Revenue forecast
        2. Deal volume prediction
        3. Key trends to watch
        4. Confidence level and factors

        Format as JSON:
        {
          "revenue_forecast": "amount",
          "deal_volume": "number",
          "trends": ["trend1", "trend2"],
          "confidence_level": "percentage",
          "factors": ["factor1", "factor2"]
        }
      `;

      // Remove non-ASCII characters and trim
      let asciiPrompt = prompt.trim();
      asciiPrompt = asciiPrompt.normalize('NFKD').replace(/[^\x00-\x7F]/g, '');
      asciiPrompt = asciiPrompt.replace(/[\u2018\u2019\u201C\u201D]/g, "'");
      asciiPrompt = asciiPrompt.replace(/[\u2013\u2014]/g, '-');

      const result = await model.generateContent(asciiPrompt);
      const response = await result.response;
      const text = response.text();

      try {
        return JSON.parse(text);
      } catch (parseError) {
        return {
          revenue_forecast: 'Analysis in progress',
          deal_volume: 'Calculating...',
          trends: ['Historical trend analysis'],
          confidence_level: 'Medium',
          factors: ['Market conditions', 'Historical performance']
        };
      }
    } catch (error) {
      console.error('Error generating forecast:', error);
      return {
        revenue_forecast: 'Unable to forecast',
        deal_volume: 'Data insufficient',
        trends: ['Manual analysis recommended'],
        confidence_level: 'Low',
        factors: ['Insufficient data', 'Service unavailable']
      };
    }
  }
}
