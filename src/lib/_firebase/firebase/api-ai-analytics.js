import { GoogleGenerativeAI } from '@google/generative-ai';
import { adminDb } from '../../lib/firebaseAdmin';
import { createProtectedRoute } from '../../lib/middleware';

// Initialize Google AI
const genAI = new GoogleGenerativeAI(process.env.GOOGLE_AI_API_KEY || 'your-api-key-here');

async function handler(req, res) {
  const userId = req.user.uid;
  const userData = req.user;

    if (req.method === 'POST') {
      const { analysisType, timeRange, includeTeamData } = req.body;

      // Fetch sales data based on user permissions
      let query = adminDb.collection('salesData');
      
      if (userData.role !== 'admin' && !includeTeamData) {
        query = query.where('userId', '==', userId);
      }

      // Apply time range filter
      if (timeRange) {
        const startDate = new Date();
        switch (timeRange) {
          case 'week':
            startDate.setDate(startDate.getDate() - 7);
            break;
          case 'month':
            startDate.setMonth(startDate.getMonth() - 1);
            break;
          case 'quarter':
            startDate.setMonth(startDate.getMonth() - 3);
            break;
          case 'year':
            startDate.setFullYear(startDate.getFullYear() - 1);
            break;
        }
        query = query.where('createdAt', '>=', startDate);
      }

      const snapshot = await query.get();
      const salesData = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt.toDate(),
        updatedAt: doc.data().updatedAt.toDate(),
        expectedCloseDate: doc.data().expectedCloseDate?.toDate()
      }));

      // Prepare data for AI analysis
      const dataForAI = prepareSalesDataForAI(salesData);

      // Generate AI insights based on analysis type
      let aiResponse;
      try {
        const model = genAI.getGenerativeModel({ model: "gemini-pro" });
        
        const prompt = generatePrompt(analysisType, dataForAI, timeRange);
        const result = await model.generateContent(prompt);
        const response = await result.response;
        aiResponse = response.text();
      } catch (aiError) {
        console.error('AI Generation Error:', aiError);
        // Fallback to rule-based analysis
        aiResponse = generateFallbackAnalysis(analysisType, salesData);
      }

      // Parse AI response and structure it
      const structuredInsights = parseAIResponse(aiResponse, analysisType, salesData);

      return res.status(200).json({
        insights: structuredInsights,
        dataPoints: salesData.length,
        timeRange,
        analysisType,
        generatedAt: new Date().toISOString()
      });

    } else {
      res.setHeader('Allow', ['POST']);
      return res.status(405).json({ error: `Method ${req.method} not allowed` });
    }
}

// Export the protected route with stricter rate limiting for AI operations
export default createProtectedRoute(handler, {
  requireOnboarding: true,
  rateLimit: { maxRequests: 10, windowMs: 15 * 60 * 1000 } // 10 AI requests per 15 minutes
});

function prepareSalesDataForAI(salesData) {
  // Aggregate data for AI analysis
  const summary = {
    totalRecords: salesData.length,
    totalValue: salesData.reduce((sum, record) => sum + record.dealValue, 0),
    averageDealValue: salesData.length > 0 ? salesData.reduce((sum, record) => sum + record.dealValue, 0) / salesData.length : 0,
    stageDistribution: {},
    monthlyTrends: {},
    topCustomers: {},
    productPerformance: {}
  };

  // Calculate stage distribution
  salesData.forEach(record => {
    summary.stageDistribution[record.stage] = (summary.stageDistribution[record.stage] || 0) + 1;
  });

  // Calculate monthly trends
  salesData.forEach(record => {
    const month = record.createdAt.toISOString().substring(0, 7); // YYYY-MM
    if (!summary.monthlyTrends[month]) {
      summary.monthlyTrends[month] = { count: 0, value: 0 };
    }
    summary.monthlyTrends[month].count++;
    summary.monthlyTrends[month].value += record.dealValue;
  });

  // Top customers by deal value
  salesData.forEach(record => {
    summary.topCustomers[record.customerName] = (summary.topCustomers[record.customerName] || 0) + record.dealValue;
  });

  // Product performance
  salesData.forEach(record => {
    if (record.product) {
      if (!summary.productPerformance[record.product]) {
        summary.productPerformance[record.product] = { count: 0, value: 0 };
      }
      summary.productPerformance[record.product].count++;
      summary.productPerformance[record.product].value += record.dealValue;
    }
  });

  return summary;
}

function generatePrompt(analysisType, dataForAI, timeRange) {
  const baseContext = `
    Sales Data Analysis for ${timeRange || 'all time'}:
    - Total Records: ${dataForAI.totalRecords}
    - Total Value: $${dataForAI.totalValue.toLocaleString()}
    - Average Deal Value: $${dataForAI.averageDealValue.toFixed(2)}
    - Stage Distribution: ${JSON.stringify(dataForAI.stageDistribution)}
    - Monthly Trends: ${JSON.stringify(dataForAI.monthlyTrends)}
    - Top Customers: ${JSON.stringify(Object.entries(dataForAI.topCustomers).slice(0, 5))}
    - Product Performance: ${JSON.stringify(dataForAI.productPerformance)}
  `;

  switch (analysisType) {
    case 'performance':
      return `${baseContext}
        
        Analyze the sales performance and provide:
        1. Key performance insights (2-3 bullet points)
        2. Growth trends and patterns
        3. Top performing areas
        4. Areas needing attention
        5. Specific actionable recommendations
        
        Format your response as JSON with keys: summary, trends, strengths, weaknesses, recommendations`;

    case 'forecasting':
      return `${baseContext}
        
        Based on the sales data trends, provide:
        1. Sales forecast for next month/quarter
        2. Expected growth rate
        3. Potential risks and opportunities
        4. Recommended actions to achieve targets
        
        Format your response as JSON with keys: forecast, growthRate, risks, opportunities, actions`;

    case 'optimization':
      return `${baseContext}
        
        Analyze the sales process and suggest optimizations:
        1. Pipeline efficiency analysis
        2. Stage conversion recommendations
        3. Resource allocation suggestions
        4. Process improvement opportunities
        
        Format your response as JSON with keys: efficiency, conversions, resources, improvements`;

    default:
      return `${baseContext}
        
        Provide a comprehensive sales analysis including performance insights, trends, and recommendations.
        Format your response as JSON with keys: summary, insights, recommendations`;
  }
}

function generateFallbackAnalysis(analysisType, salesData) {
  // Rule-based fallback analysis when AI is not available
  const totalValue = salesData.reduce((sum, record) => sum + record.dealValue, 0);
  const avgDealValue = salesData.length > 0 ? totalValue / salesData.length : 0;
  
  const stageDistribution = {};
  salesData.forEach(record => {
    stageDistribution[record.stage] = (stageDistribution[record.stage] || 0) + 1;
  });

  const closedWon = stageDistribution['closed-won'] || 0;
  const conversionRate = salesData.length > 0 ? (closedWon / salesData.length * 100).toFixed(1) : 0;

  return JSON.stringify({
    summary: `Analysis of ${salesData.length} sales records with total value of $${totalValue.toLocaleString()}`,
    insights: [
      `Average deal value: $${avgDealValue.toFixed(2)}`,
      `Conversion rate: ${conversionRate}%`,
      `Most common stage: ${Object.keys(stageDistribution).reduce((a, b) => stageDistribution[a] > stageDistribution[b] ? a : b, 'prospect')}`
    ],
    recommendations: [
      'Focus on improving conversion rates in early stages',
      'Analyze successful deals to replicate winning strategies',
      'Consider increasing deal values through upselling'
    ]
  });
}

function parseAIResponse(aiResponse, analysisType, salesData) {
  try {
    // Try to parse as JSON first
    const parsed = JSON.parse(aiResponse);
    return parsed;
  } catch (error) {
    // If not JSON, structure the text response
    return {
      summary: aiResponse.substring(0, 200) + '...',
      insights: aiResponse.split('\n').filter(line => line.trim().length > 0).slice(0, 5),
      recommendations: [
        'Review the detailed analysis above',
        'Focus on data-driven decision making',
        'Monitor key performance indicators regularly'
      ],
      rawResponse: aiResponse
    };
  }
}
