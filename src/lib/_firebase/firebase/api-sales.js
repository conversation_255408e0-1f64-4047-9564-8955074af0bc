import { adminDb } from '../../lib/firebaseAdmin';
import { createProtectedRoute } from '../../lib/middleware';

async function handler(req, res) {
  const userId = req.user.uid;
  const userData = req.user;

    if (req.method === 'POST') {
      // Create new sales record
      const {
        customerName,
        dealValue,
        stage,
        expectedCloseDate,
        product,
        notes,
        probability
      } = req.body;

      // Validation
      if (!customerName || !dealValue || !stage) {
        return res.status(400).json({ error: 'Customer name, deal value, and stage are required' });
      }

      const salesRecord = {
        customerName,
        dealValue: parseFloat(dealValue),
        stage,
        expectedCloseDate: expectedCloseDate ? new Date(expectedCloseDate) : null,
        product: product || '',
        notes: notes || '',
        probability: probability || 50,
        userId,
        createdBy: userData.displayName || userData.email,
        createdAt: new Date(),
        updatedAt: new Date(),
        status: 'active'
      };

      const docRef = await adminDb.collection('salesData').add(salesRecord);

      return res.status(201).json({
        id: docRef.id,
        ...salesRecord,
        createdAt: salesRecord.createdAt.toISOString(),
        updatedAt: salesRecord.updatedAt.toISOString(),
        expectedCloseDate: salesRecord.expectedCloseDate?.toISOString()
      });

    } else if (req.method === 'GET') {
      // Get sales records
      const { startDate, endDate, stage, userId: filterUserId } = req.query;

      let query = adminDb.collection('salesData');

      // Apply filters
      if (userData.role !== 'admin' && !filterUserId) {
        // Regular users can only see their own records
        query = query.where('userId', '==', userId);
      } else if (filterUserId && userData.role === 'admin') {
        // Admins can filter by specific user
        query = query.where('userId', '==', filterUserId);
      }

      if (stage) {
        query = query.where('stage', '==', stage);
      }

      if (startDate) {
        query = query.where('createdAt', '>=', new Date(startDate));
      }

      if (endDate) {
        query = query.where('createdAt', '<=', new Date(endDate));
      }

      query = query.orderBy('createdAt', 'desc');

      const snapshot = await query.get();
      const salesRecords = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt.toDate().toISOString(),
        updatedAt: doc.data().updatedAt.toDate().toISOString(),
        expectedCloseDate: doc.data().expectedCloseDate?.toDate().toISOString()
      }));

      return res.status(200).json(salesRecords);

    } else if (req.method === 'PUT') {
      // Update sales record
      const { recordId } = req.query;
      const updates = req.body;

      if (!recordId) {
        return res.status(400).json({ error: 'Record ID is required' });
      }

      // Check if user owns the record or is admin
      const recordDoc = await adminDb.collection('salesData').doc(recordId).get();
      if (!recordDoc.exists) {
        return res.status(404).json({ error: 'Sales record not found' });
      }

      const recordData = recordDoc.data();
      if (recordData.userId !== userId && userData.role !== 'admin') {
        return res.status(403).json({ error: 'Permission denied' });
      }

      // Prepare update data
      const updateData = {
        ...updates,
        updatedAt: new Date()
      };

      // Convert date strings to Date objects
      if (updates.expectedCloseDate) {
        updateData.expectedCloseDate = new Date(updates.expectedCloseDate);
      }

      if (updates.dealValue) {
        updateData.dealValue = parseFloat(updates.dealValue);
      }

      await adminDb.collection('salesData').doc(recordId).update(updateData);

      return res.status(200).json({ message: 'Sales record updated successfully' });

    } else if (req.method === 'DELETE') {
      // Delete sales record
      const { recordId } = req.query;

      if (!recordId) {
        return res.status(400).json({ error: 'Record ID is required' });
      }

      // Check if user owns the record or is admin
      const recordDoc = await adminDb.collection('salesData').doc(recordId).get();
      if (!recordDoc.exists) {
        return res.status(404).json({ error: 'Sales record not found' });
      }

      const recordData = recordDoc.data();
      if (recordData.userId !== userId && userData.role !== 'admin') {
        return res.status(403).json({ error: 'Permission denied' });
      }

      await adminDb.collection('salesData').doc(recordId).delete();

      return res.status(200).json({ message: 'Sales record deleted successfully' });

    } else {
      res.setHeader('Allow', ['GET', 'POST', 'PUT', 'DELETE']);
      return res.status(405).json({ error: `Method ${req.method} not allowed` });
    }
}

// Export the protected route with rate limiting
export default createProtectedRoute(handler, {
  requireOnboarding: true,
  rateLimit: { maxRequests: 50, windowMs: 15 * 60 * 1000 } // 50 requests per 15 minutes
});
