// Database models and helper functions
import { 
  collection, 
  doc, 
  getDoc, 
  setDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  getDocs,
  addDoc,
  serverTimestamp 
} from 'firebase/firestore';
import { db } from './firebase';

// Collections
export const COLLECTIONS = {
  USERS: 'users',
  INVITATIONS: 'invitations',
  SALES_DATA: 'salesData',
  ONBOARDING: 'onboarding'
};

// User model
export const createUser = async (uid, userData) => {
  const userRef = doc(db, COLLECTIONS.USERS, uid);
  const user = {
    ...userData,
    createdAt: serverTimestamp(),
    updatedAt: serverTimestamp(),
    role: 'user', // default role
    isOnboarded: false
  };
  await setDoc(userRef, user);
  return user;
};

export const getUser = async (uid) => {
  const userRef = doc(db, COLLECTIONS.USERS, uid);
  const userSnap = await getDoc(userRef);
  return userSnap.exists() ? { id: userSnap.id, ...userSnap.data() } : null;
};

export const updateUser = async (uid, updates) => {
  const userRef = doc(db, COLLECTIONS.USERS, uid);
  await updateDoc(userRef, {
    ...updates,
    updatedAt: serverTimestamp()
  });
};

// Invitation model
export const createInvitation = async (invitationData) => {
  const invitationRef = collection(db, COLLECTIONS.INVITATIONS);
  const invitation = {
    ...invitationData,
    createdAt: serverTimestamp(),
    isUsed: false,
    expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days from now
  };
  const docRef = await addDoc(invitationRef, invitation);
  return { id: docRef.id, ...invitation };
};

export const getInvitation = async (code) => {
  const q = query(
    collection(db, COLLECTIONS.INVITATIONS), 
    where('code', '==', code),
    where('isUsed', '==', false)
  );
  const querySnapshot = await getDocs(q);
  
  if (querySnapshot.empty) return null;
  
  const doc = querySnapshot.docs[0];
  const invitation = { id: doc.id, ...doc.data() };
  
  // Check if invitation is expired
  if (invitation.expiresAt.toDate() < new Date()) {
    return null;
  }
  
  return invitation;
};

export const useInvitation = async (invitationId, userId) => {
  const invitationRef = doc(db, COLLECTIONS.INVITATIONS, invitationId);
  await updateDoc(invitationRef, {
    isUsed: true,
    usedBy: userId,
    usedAt: serverTimestamp()
  });
};

// Sales data model
export const createSalesRecord = async (salesData) => {
  const salesRef = collection(db, COLLECTIONS.SALES_DATA);
  const record = {
    ...salesData,
    createdAt: serverTimestamp(),
    updatedAt: serverTimestamp()
  };
  const docRef = await addDoc(salesRef, record);
  return { id: docRef.id, ...record };
};

export const getSalesData = async (filters = {}) => {
  let q = collection(db, COLLECTIONS.SALES_DATA);
  
  if (filters.userId) {
    q = query(q, where('userId', '==', filters.userId));
  }
  
  if (filters.dateFrom) {
    q = query(q, where('date', '>=', filters.dateFrom));
  }
  
  if (filters.dateTo) {
    q = query(q, where('date', '<=', filters.dateTo));
  }
  
  const querySnapshot = await getDocs(q);
  return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
};

export const updateSalesRecord = async (recordId, updates) => {
  const recordRef = doc(db, COLLECTIONS.SALES_DATA, recordId);
  await updateDoc(recordRef, {
    ...updates,
    updatedAt: serverTimestamp()
  });
};

export const deleteSalesRecord = async (recordId) => {
  const recordRef = doc(db, COLLECTIONS.SALES_DATA, recordId);
  await deleteDoc(recordRef);
};

// Onboarding model
export const completeOnboarding = async (userId, onboardingData) => {
  const onboardingRef = doc(db, COLLECTIONS.ONBOARDING, userId);
  await setDoc(onboardingRef, {
    ...onboardingData,
    completedAt: serverTimestamp()
  });
  
  // Update user's onboarding status
  await updateUser(userId, { isOnboarded: true });
};
