export const appvars = {
    appname : 'TFLOUU',
    port: '3000',
}
const appname = 'tflouu';

export const vars = {
    db : {
        dbName: 'toursdb',
        TflouuDBName: 'toursdb',
        collection: {
            users: appname + '.main.dim.users',
            customers: appname + '.main.dim.customers',
            variables: appname + '.main.dim.variables',
            pricingPlans: appname + '.main.dim.pricingPlans', // tflouu.main.dim.priceplans
            _act_schemaInfo: '.a.schemainfo',
        },
        actCollections: {
            schemaInfo: '.a.schemainfo',
            workspaces: '.app.workspaces',
            workspaceselections: '.app.workspaces.selections',
            organizations: '.app.organizations',
        },
    },
    token : {
        tokenlifeTime: '1m',
        refreshtokenLifeTime: '30d',
    }
}

export const siteConfig = {
    name: "Insights",
    url: "https://tflouu.com",
    description: "The only reporting and audit dashboard you will ever need.",
  }

