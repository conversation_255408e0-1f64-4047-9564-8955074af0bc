import React from 'react';
import Sidebar from '@/components/Sidebar';
import { But<PERSON> } from '@/components/ui/button';
import { Avatar } from '@/components/ui/avatar';
import { Bell, Mail, Menu } from 'lucide-react';
import {
  HomeIcon,
  ChartBarIcon,
  UsersIcon,
  CogIcon,
  ArrowRightOnRectangleIcon,
  Bars3Icon,
  XMarkIcon,
  BellIcon,
  UserCircleIcon,
  ShoppingBagIcon,
  BuildingOfficeIcon
} from '@heroicons/react/24/outline';
import Link from 'next/link';

const topRightIconsDefault = () => (
  <div className="ml-4 flex items-center md:ml-6">
    <button className="bg-white p-1 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
      <BellIcon className="h-6 w-6" />
    </button>
  </div>
)

const AdminLayout = ({
  isDrawerOpen,
  toggleDrawer,
  title,
  breadCrumbs = ['Home'],
  topRightIcons,
  children,
}) => {
  
  return (
  <div className="h-screen max-w-[2048px] mx-auto bg-gray-100">
    <div className="flex h-full min-w-[600px] overflow-x-auto">
      {/* Sidebar Component */}
      <Sidebar isDrawerOpen={isDrawerOpen} toggleDrawer={toggleDrawer} />

      {/* Main Content Area */}
      <div className="flex flex-col flex-1 overflow-hidden">
        {/* Sticky Header */}
        <header className="flex items-center justify-between h-16 px-6 bg-white border-b border-gray-200 sticky top-0 z-10">
          <div className="flex items-center">
            <Button variant="ghost" size="icon" onClick={toggleDrawer} className="mr-4 md:hidden">
              <Menu className="h-6 w-6" />
            </Button>
            <div className="flex flex-col">
              <h2 className="text-2xl font-bold text-gray-800 whitespace-nowrap truncate">{title || 'Dashboard Overview'}</h2>
              <nav className="text-sm text-gray-500">
                <ol className="flex list-none p-0 m-0">
                  <li className="flex items-center">
                    <Link href="/" className="hover:underline">{breadCrumbs[0] || 'Home'}</Link>
                    <span className="mx-2">/</span>
                  </li>
                  <li className="text-gray-700">{breadCrumbs[1] || 'Dashboard'}</li>
                </ol>
              </nav>
            </div>
          </div>
          {topRightIcons || topRightIconsDefault()}
          {/* <div className="flex items-center space-x-4">
            <Button variant="outline">New Report</Button>
            <Button variant="default">Add User</Button>
            <Button variant="ghost" size="icon">
              <Bell className="h-5 w-5" />
            </Button>
            <Button variant="ghost" size="icon">
              <Mail className="h-5 w-5" />
            </Button>
            <Avatar className="h-8 w-8">
              <span className="flex h-full w-full items-center justify-center rounded-full bg-gray-300 text-sm">U</span>
            </Avatar>
          </div> */}
        </header>

        {/* Main Content with Scroll */}
        <main className="flex-1 overflow-y-auto p-6">
          {children}
        </main>
      </div>
    </div>
  </div>
)};

export default AdminLayout;