import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from 'recharts';

const MonthlyTrendsChart = () => {
  const [monthlyTrendsData, setMonthlyTrendsData] = useState([]);
  const [monthlyTrendsLoading, setMonthlyTrendsLoading] = useState(true);

  const currentYear = new Date().getFullYear();
  const previousYear = currentYear - 1;

  useEffect(() => {
    const fetchMonthlyTrends = async () => {
      setMonthlyTrendsLoading(true);
      try {
        const response = await fetch('/api/hotels/stats?w=ayliksatislarverezervasyon');
        if (response.ok) {
          const data = await response.json();
          setMonthlyTrendsData(data.data || []);
        } else {
          console.error('Failed to fetch monthly trends:', response.statusText);
        }
      } catch (error) {
        console.error('Error fetching monthly trends:', error);
      } finally {
        setMonthlyTrendsLoading(false);
      }
    };
    fetchMonthlyTrends();
  }, []);

  return (
    <Card>
      <CardHeader>
        <CardTitle>Monthly Trends</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-64">
          {monthlyTrendsLoading ? (
            <div className="flex items-center justify-center h-full">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            </div>
          ) : (
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={(() => {
                const chartData = [];
                const orderedMonths = [
                  'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                  'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
                ];

                // Create a map for easier lookup by month
                const dataMap = {};
                monthlyTrendsData.forEach(item => {
                  const monthName = new Date(item.currentYear, item.month - 1).toLocaleString('en-US', { month: 'short' });
                  dataMap[monthName] = item;
                });

                orderedMonths.forEach(monthName => {
                  const item = dataMap[monthName] || {
                    month: monthName,
                    revenue_currentYear: 0,
                    reservations_currentYear: 0,
                    revenue_prevYear: 0,
                    reservations_prevYear: 0,
                    currentYear: currentYear,
                    prevYear: previousYear
                  };
                  chartData.push({
                    month: monthName,
                    [`${item.currentYear} Revenue`]: item.revenue_currentYear,
                    [`${item.prevYear} Revenue`]: item.revenue_prevYear,
                  });
                });
                return chartData;
              })()}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip
                  formatter={(value, name) => [
                    new Intl.NumberFormat('tr-TR', { style: 'currency', currency: 'TRY', minimumFractionDigits: 0, maximumFractionDigits: 0 }).format(value),
                    name
                  ]}
                />
                <Bar dataKey={`${previousYear} Revenue`} fill="#94a3b8" name={`${previousYear} Revenue`} />
                <Bar dataKey={`${currentYear} Revenue`} fill="#1e40af" name={`${currentYear} Revenue`} />
              </BarChart>
            </ResponsiveContainer>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default MonthlyTrendsChart;