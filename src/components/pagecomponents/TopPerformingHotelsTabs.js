import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Content } from "@/components/ui/tabs";

import { useRouter } from 'next/router';
const now = new Date();
const currentYear = now.getFullYear();
const currentMonth = (now.getMonth() + 1).toString().padStart(2, "0");
const currentDonem = `${currentYear}-${currentMonth}`;

const fetchHotels = async (donem) => {
  const res = await fetch(`/api/hotels/stats?w=tophotelsbydonem&donem=${donem}`);
  if (!res.ok) return [];
  return res.json();
};

const TopPerformingHotelsTabs = () => {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("month");
  const [loadedTabs, setLoadedTabs] = useState({ month: false, year: false });
  const [hotelsMonth, setHotelsMonth] = useState([]);
  const [hotelsYear, setHotelsYear] = useState([]);
  const [loading, setLoading] = useState(false);

  // Fetch data when tab changes and not loaded yet
  useEffect(() => {
    const load = async () => {
      setLoading(true);
      if (activeTab === "month" && !loadedTabs.month) {
        const data = await fetchHotels(currentDonem);
        setHotelsMonth(data.data);
        setLoadedTabs((prev) => ({ ...prev, month: true }));
      }
      if (activeTab === "year" && !loadedTabs.year) {
        const data = await fetchHotels(`${currentYear}`);
        setHotelsYear(data.data);
        setLoadedTabs((prev) => ({ ...prev, year: true }));
      }
      setLoading(false);
    };
    load();
    // eslint-disable-next-line
  }, [activeTab]);

  const renderHotelList = (hotels) => (
    <div className="space-y-3">
      {hotels && hotels.length > 0 ? hotels.map((hotel, i) => (
          <div key={hotel.TESIS_ID}
                    onClick={() => router.push(`/hotels-domestic/${hotel.TESIS_ID}`)} 
                    className="flex items-center justify-between p-3 bg-gray-50 rounded-lg shadow-sm cursor-pointer">
          <div>
            <p className="font-medium">{hotel.TESIS_ADI}</p>
            <p className="text-xs text-gray-500">{hotel.BOLGE_ADI} {hotel.ALT_BOLGE_ADI ? `- ${hotel.ALT_BOLGE_ADI}` : ""} {' | '} {hotel.KATEGORI}</p>
          </div>
          <div className="text-right">
            <p className="font-semibold">₺{hotel.toplamCiro?.toLocaleString("tr-TR")}</p>
            <p className="text-xs text-blue-600">{hotel.toplamRezvAdedi} rezervasyon</p>
          </div>
        </div>
      )) : (
        <div className="text-center text-gray-400 text-sm py-8">Kayıt bulunamadı</div>
      )}
    </div>
  );

  return (
    <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
      <TabsList className="w-full flex bg-gray-100 rounded-lg mb-4">
        <TabsTrigger
          value="month"
          className="flex-1 py-2 px-4 rounded-lg text-sm font-semibold transition-all duration-200 data-[state=active]:bg-white data-[state=active]:shadow data-[state=active]:text-blue-600"
        >
          {currentDonem}
        </TabsTrigger>
        <TabsTrigger
          value="year"
          className="flex-1 py-2 px-4 rounded-lg text-sm font-semibold transition-all duration-200 data-[state=active]:bg-white data-[state=active]:shadow data-[state=active]:text-blue-600"
        >
          {currentYear}
        </TabsTrigger>
      </TabsList>
      <TabsContent value="month">
        <div className="min-h-[180px] animate-slide-up">
          {loading && activeTab === "month" ? (
            <div className="text-center text-gray-400 py-8">Yükleniyor...</div>
          ) : renderHotelList(hotelsMonth)}
        </div>
      </TabsContent>
      <TabsContent value="year">
        <div className="min-h-[180px] animate-slide-up">
          {loading && activeTab === "year" ? (
            <div className="text-center text-gray-400 py-8">Yükleniyor...</div>
          ) : renderHotelList(hotelsYear)}
        </div>
      </TabsContent>
      <style jsx>{`
        .animate-slide-up {
          animation: slideUp 0.5s cubic-bezier(0.4,0,0.2,1);
        }
        @keyframes slideUp {
          from {
            opacity: 0;
            transform: translateY(40px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
      `}</style>
    </Tabs>
  );
};

export const TopPerformingHotelsTabz = () => <TopPerformingHotelsTabs />;