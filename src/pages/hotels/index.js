import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { GripHorizontal, MapPin, Star, Eye, TrendingUp } from 'lucide-react';
import ProtectedRoute from '@/components/ProtectedRoute';
import AdminLayout from '@/components/layouts/AdminLayout';
import { Button } from '@/components/ui/button';
import { Avatar } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Bell, Mail, Menu } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/router';

const HotelsPage = () => {
  const router = useRouter();
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [leftPanelWidth, setLeftPanelWidth] = useState(30);
  const [isResizing, setIsResizing] = useState(false);
  const [hotels, setHotels] = useState([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({});
  const containerRef = useRef(null);

  const MIN_LEFT_PANEL_WIDTH_PX = 300;
  const MIN_RIGHT_PANEL_WIDTH_PX = 500;

  const toggleDrawer = () => setIsDrawerOpen(!isDrawerOpen);

  useEffect(() => {
    fetchHotels();
    fetchStats();
  }, []);

  const fetchHotels = async () => {
    try {
      const response = await fetch('/api/hotels/stats?w=tophotelsbydonem&limit=20');
      if (response.ok) {
        const result = await response.json();
        setHotels(result.data || []);
      }
    } catch (error) {
      console.error('Error fetching hotels:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await fetch('/api/hotels/stats?w=hotelstatistics');
      if (response.ok) {
        const result = await response.json();
        setStats(result);
      }
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const formatNumber = (value) => {
    return new Intl.NumberFormat('tr-TR').format(value);
  };

  const handleMouseDown = () => setIsResizing(true);

  const handleMouseMove = (e) => {
    if (!isResizing) return;
    if (containerRef.current) {
      const containerRect = containerRef.current.getBoundingClientRect();
      let newWidthPx = e.clientX - containerRect.left;
      const maxLeftPanelWidthPx = containerRect.width - MIN_RIGHT_PANEL_WIDTH_PX;
      newWidthPx = Math.max(MIN_LEFT_PANEL_WIDTH_PX, Math.min(newWidthPx, maxLeftPanelWidthPx));
      const newWidthPercent = (newWidthPx / containerRect.width) * 100;
      setLeftPanelWidth(newWidthPercent);
    }
  };

  const handleMouseUp = () => setIsResizing(false);

  useEffect(() => {
    if (isResizing) {
      window.addEventListener('mousemove', handleMouseMove);
      window.addEventListener('mouseup', handleMouseUp);
    } else {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
    }
    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isResizing]);

  const TopRightIcons = () => ( 
    <div className="flex items-center space-x-4">
      <Button variant="outline">New Report</Button>
      <Button variant="default">Add User</Button>
      <Button variant="ghost" size="icon">
        <Bell className="h-5 w-5" />
      </Button>
      <Button variant="ghost" size="icon">
        <Mail className="h-5 w-5" />
      </Button>
      <Avatar className="h-8 w-8">
        {/* Placeholder for user avatar */}
        <span className="flex h-full w-full items-center justify-center rounded-full bg-gray-300 text-sm">U</span>
      </Avatar>
    </div>
  )

  return (
    <ProtectedRoute>
    <AdminLayout isDrawerOpen={isDrawerOpen} 
      toggleDrawer={toggleDrawer} 
      title={'Hotels'} 
      breadCrumbs={['Home', 'Hotels']}
      // topRightIcons={<TopRightIcons />} 
      >
      <div ref={containerRef} className="flex flex-1 overflow-hidden -m-6">
        {/* Left Panel */}
        <div style={{ width: `${leftPanelWidth}%` }} className="flex flex-col flex-shrink-0 p-6 overflow-y-auto bg-white border-r border-gray-200">
          <h3 className="text-xl font-semibold mb-4">Static Left Panel</h3>
          <p>This panel is static and will not scroll vertically.</p>
          <p>It has a minimum width of {MIN_LEFT_PANEL_WIDTH_PX}px.</p>
          <div className="mt-4 space-y-2">
            <Card>
              <CardHeader><CardTitle>Left Card 1</CardTitle></CardHeader>
              <CardContent><p>Content for left card 1.</p></CardContent>
            </Card>
            <Card>
              <CardHeader><CardTitle>Left Card 2</CardTitle></CardHeader>
              <CardContent><p>Content for left card 2.</p></CardContent>
            </Card>
            <Card>
              <CardHeader><CardTitle>Left Card 3</CardTitle></CardHeader>
              <CardContent><p>Content for left card 3.</p></CardContent>
            </Card>
            <Card>
              <CardHeader><CardTitle>Left Card 4</CardTitle></CardHeader>
              <CardContent><p>Content for left card 4.</p></CardContent>
            </Card>
          </div>
        </div>

        {/* Resizer Bar */}
        <div
          className="w-1 bg-gray-300 cursor-ew-resize flex items-center justify-center"
          onMouseDown={handleMouseDown}
        >
          <GripHorizontal className="h-[50px] w-5 text-gray-800" />
        </div>

        {/* Right Panel */}
        <div className="flex-1 p-6 overflow-y-auto">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Star className="h-5 w-5" />
                  <span>Total Hotels</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-4xl font-bold">{formatNumber(stats.toplam_tesis || 0)}</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Eye className="h-5 w-5" />
                  <span>Listed Hotels</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-4xl font-bold">{formatNumber(stats.listelenen_tesisler || 0)}</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <TrendingUp className="h-5 w-5" />
                  <span>Listing Rate</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-4xl font-bold">
                  {stats.toplam_tesis ?
                    Math.round((stats.listelenen_tesisler / stats.toplam_tesis) * 100) : 0}%
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Top Hotels List */}
          <Card>
            <CardHeader>
              <CardTitle>Top Performing Hotels</CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex items-center justify-center h-32">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                </div>
              ) : (
                <div className="space-y-4">
                  {hotels.map((hotel, index) => (
                    <div
                      key={hotel.TESIS_ID}
                      className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                      onClick={() => router.push(`/hotels/${hotel.TESIS_ID}`)}
                    >
                        <div className="flex items-center space-x-4">
                          <div className="flex items-center justify-center w-8 h-8 bg-blue-100 text-blue-600 rounded-full font-semibold">
                            {index + 1}
                          </div>
                          <div>
                            <h3 className="font-semibold text-lg">{hotel.TESIS_ADI}</h3>
                            <div className="flex items-center space-x-2 text-sm text-gray-600">
                              <MapPin className="h-4 w-4" />
                              <span>{hotel.BOLGE_ADI}</span>
                              {hotel.ALT_BOLGE_ADI && (
                                <>
                                  <span>•</span>
                                  <span>{hotel.ALT_BOLGE_ADI}</span>
                                </>
                              )}
                            </div>
                            {hotel.KATEGORI && (
                              <Badge variant="outline" className="mt-1">
                                {hotel.KATEGORI}
                              </Badge>
                            )}
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-lg font-semibold text-green-600">
                            {formatCurrency(hotel.toplamCiro)}
                          </div>
                          <div className="text-sm text-gray-500">
                            {formatNumber(hotel.toplamRezvAdedi)} reservations
                          </div>
                        </div>
                      </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </AdminLayout>
    </ProtectedRoute>
  );
};

export default HotelsPage;