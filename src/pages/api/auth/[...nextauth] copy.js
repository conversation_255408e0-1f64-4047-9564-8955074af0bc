import NextAuth from "next-auth";
import GoogleProvider from "next-auth/providers/google";
import CredentialsProvider from "next-auth/providers/credentials";
import { MongoDBAdapter } from "@next-auth/mongodb-adapter";
import clientPromise, { getCollection } from "@/lib/db/mongodb";
import bcrypt from "bcryptjs";
import { vars } from "@/lib/constants";
import * as fnxAuth from "@/lib/fnx/fnx.auth";

export const authOptions = {
  adapter: MongoDBAdapter(clientPromise),
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID || "",
      clientSecret: process.env.GOOGLE_CLIENT_SECRET || "",
    }),
    CredentialsProvider({
      // id: "tflouu-signin",
      id: "credentials",
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        try {
          if (!credentials?.email || !credentials?.password) {
            return null;
          }

          const dbConn = await clientPromise;
          const users = await getCollection(vars.db.collection.users);
          const user = await users.findOne({ email: credentials.email });

          if (!user) {
            return null;
          }
          // console.log('user', user);
          const isPasswordValid = await bcrypt.compare(
            credentials.password,
            user.password
          );

          if (!isPasswordValid) {
            return null;
          }

          const jwtPayload = {
            id: user._id.toString(),
            email: user.email,
            fullName: user.fullName,
            custName: user.custName,
            customerId: user.customerId,
            clientId: user.clientId, // Eklendi
            clientSchema: user.clientSchema,
            role: user.role,
          };

          const token = await fnxAuth.jwtx.sign({payload: jwtPayload, lifeTime: vars.token.tokenlifeTime}); //'2h' 20s
          const refreshToken = await fnxAuth.jwtx.sign({payload: jwtPayload, lifeTime: vars.token.refreshtokenLifeTime});
          // console.log('refreshToken 1', refreshToken); 

          await fnxAuth.saveToken({ dbConn, payload: jwtPayload, token: refreshToken, saveLogin: true })

          const resp = {
            ...jwtPayload,
            token,
            refreshToken,
            image: user.image,
          };
          return resp;
        } catch (error) {
          console.error("Auth error:", error);
          return null;
        }
      }
    }),
  ],
  callbacks: {
    async signIn({ user, account, profile }) {
      if (account?.provider === "google") {
        try {
          const users = await getCollection(vars.db.collection.users);
          const existingUser = await users.findOne({ email: user.email });

          if (!existingUser) {
            // Create a new user if they don't exist
            await users.insertOne({
              email: user.email,
              name: user.name,
              image: user.image,
              role: 'user',
              createdAt: new Date(),
            });
          }
        } catch (error) {
          console.error("Error during Google sign in:", error);
        }
      }
      return true;
    },
    
    async jwt(propz) {
      const { token, user } = propz;
      if (user) {
        // console.log('jwt refreshToken 2 user', user); 
        token.id = user.id;
        token.customerId = user.customerId;
        token.custName = user.custName;
        token.fullName = user.fullName;
        token.role = user.role;
        token.clientId = user.clientId;
        token.clientSchema = user.clientSchema; // Eklendi
        token.token = user.token;
        token.refreshToken = user.refreshToken;
      }
      return token;
    },
    async session({ session, token }) {
      if (token) {
        // console.log('session refreshToken 3 token', token);
        session.user.id = token.id;
        session.user.customerId = token.customerId;
        session.user.role = token.role;
        session.user.custName = token.custName;
        session.user.fullName = token.fullName;
        session.user.clientId = token.clientId;
        session.user.clientSchema = token.clientSchema; // Eklendi
        session.user.token = token.token;
        session.user.refreshToken = token.refreshToken;
      }
      // console.log('session refreshToken 3 session', session); 
      return session;
    },
  },
  pages: {
    signIn: '/auth/signin',
    error: '/auth/signin',
  },
  session: {
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  secret: process.env.NEXTAUTH_SECRET || "tflouu-secret-key",
};

export default NextAuth(authOptions);
