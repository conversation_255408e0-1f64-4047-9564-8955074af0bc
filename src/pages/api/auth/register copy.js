import { signIn } from "@/lib/fnx/fnx.auth";
import clientPromise from "@/lib/db/mongodb";

// TitleCase dönüşümü için yardımcı fonksiyon
const toTitleCase = (str) => {
  return str
    .toLowerCase()
    .split(' ')
    .map(word => {
      // Özel durumlar için kontrol (ve, veya, ile, of, in, at gibi bağlaçları küçük harfle bırak)
      const lowercaseWords = ['ve', 'veya', 'ile', 'of', 'in', 'at', 'the', 'and', 'or'];
      if (lowercaseWords.includes(word)) return word;
      return word.charAt(0).toUpperCase() + word.slice(1);
    })
    .join(' ');
};

export default async function handler(req, res) {
  if (req.method !== "POST") {
    return res.status(405).json({ message: "Method not allowed" });
  }
  try {
    const { name, email, password } = req.body;
    console.log('name, email, password', name, email, password);
    if (!name || !email || !password) {
      return res.status(400).json({ message: "Missing required fields" });
    }
    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({ message: "Invalid email format" });
    }
    // Validate password strength
    if (password.length < 8) {
      return res.status(400).json({ message: "Password must be at least 8 characters" });
    }
    try {
      const dbConn = await clientPromise;
      // İsmi TitleCase formatına dönüştür
      const formattedName = toTitleCase(name);
      const act = await signIn({ dbConn, name: formattedName, email, password });
      return res.status(201).json({
        ...act
      });
    } catch (e) {
      return res.status(400).json({ message: e.message });
    }
  } catch (error) {
    console.error("Registration error:", error);
    return res.status(500).json({ message: "Internal server error: " + error.message });
  }
}
