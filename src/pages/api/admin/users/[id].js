import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]';
import { UserModel } from '@/lib/db/models/User';
import { ObjectId } from 'mongodb';

export default async function handler(req, res) {
  const session = await getServerSession(req, res, authOptions);

  if (!session) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  // Only admins can manage users
  if (session.user.role !== 'admin') {
    return res.status(403).json({ message: 'Admin access required' });
  }

  const { method } = req;
  const { id } = req.query;

  if (!ObjectId.isValid(id)) {
    return res.status(400).json({ message: 'Invalid user ID' });
  }

  try {
    switch (method) {
      case 'GET':
        await handleGet(req, res, id);
        break;
      case 'PUT':
        await handlePut(req, res, session, id);
        break;
      case 'DELETE':
        await handleDelete(req, res, session, id);
        break;
      default:
        res.setHeader('Allow', ['GET', 'PUT', 'DELETE']);
        res.status(405).end(`Method ${method} Not Allowed`);
    }
  } catch (error) {
    console.error('Admin User API error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
}

async function handleGet(req, res, id) {
  const user = await UserModel.findById(id);

  if (!user) {
    return res.status(404).json({ message: 'User not found' });
  }

  // Remove sensitive information
  const { password, ...userResponse } = user;

  res.status(200).json(userResponse);
}

async function handlePut(req, res, session, id) {
  // Prevent users from modifying themselves in certain ways
  if (id === session.user.id) {
    const { role, isActive } = req.body;
    if (role !== undefined || isActive !== undefined) {
      return res.status(400).json({ 
        message: 'Cannot modify your own role or status' 
      });
    }
  }

  const user = await UserModel.findById(id);

  if (!user) {
    return res.status(404).json({ message: 'User not found' });
  }

  const updateData = { ...req.body };
  
  // Remove fields that shouldn't be updated directly
  delete updateData._id;
  delete updateData.createdAt;
  delete updateData.password; // Password changes should go through a different endpoint

  const result = await UserModel.update(id, updateData);

  if (result.matchedCount === 0) {
    return res.status(404).json({ message: 'User not found' });
  }

  const updatedUser = await UserModel.findById(id);
  const { password, ...userResponse } = updatedUser;
  
  res.status(200).json({
    message: 'User updated successfully',
    user: userResponse
  });
}

async function handleDelete(req, res, session, id) {
  // Prevent users from deleting themselves
  if (id === session.user.id) {
    return res.status(400).json({ 
      message: 'Cannot delete your own account' 
    });
  }

  const user = await UserModel.findById(id);

  if (!user) {
    return res.status(404).json({ message: 'User not found' });
  }

  const result = await UserModel.delete(id);

  if (result.deletedCount === 0) {
    return res.status(404).json({ message: 'User not found' });
  }

  res.status(200).json({ message: 'User deleted successfully' });
}
