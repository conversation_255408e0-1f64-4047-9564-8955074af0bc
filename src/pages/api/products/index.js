import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { ProductModel } from '@/lib/db/models/Product';
import { ObjectId } from 'mongodb';

export default async function handler(req, res) {
  const session = await getServerSession(req, res, authOptions);

  if (!session) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  const { method } = req;

  try {
    switch (method) {
      case 'GET':
        await handleGet(req, res, session);
        break;
      case 'POST':
        await handlePost(req, res, session);
        break;
      default:
        res.setHeader('Allow', ['GET', 'POST']);
        res.status(405).end(`Method ${method} Not Allowed`);
    }
  } catch (error) {
    console.error('Products API error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
}

async function handleGet(req, res, session) {
  const { 
    page = 1, 
    limit = 10, 
    category, 
    status = 'active',
    search,
    sortBy = 'createdAt',
    sortOrder = 'desc'
  } = req.query;

  // Build filter
  const filter = { status };
  
  if (category) {
    filter.category = category;
  }

  // Build sort object
  const sort = {};
  sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

  const options = {
    page: parseInt(page),
    limit: parseInt(limit),
    sort,
    search
  };

  const result = await ProductModel.findAll(filter, options);
  
  res.status(200).json(result);
}

async function handlePost(req, res, session) {
  const {
    name,
    description,
    sku,
    category,
    price,
    currency = 'USD',
    cost,
    inventory,
    specifications = {},
    images = [],
    tags = []
  } = req.body;

  // Validation
  if (!name || !sku) {
    return res.status(400).json({ 
      message: 'Name and SKU are required' 
    });
  }

  // Check if SKU already exists
  const existingProduct = await ProductModel.findBySku(sku);
  if (existingProduct) {
    return res.status(400).json({ 
      message: 'Product with this SKU already exists' 
    });
  }

  const productData = {
    name,
    description,
    sku,
    category,
    price: parseFloat(price) || 0,
    currency,
    cost: parseFloat(cost) || 0,
    margin: cost && price ? ((price - cost) / price) * 100 : 0,
    inventory: inventory || {},
    specifications,
    images,
    tags,
    createdBy: new ObjectId(session.user.id),
    updatedBy: new ObjectId(session.user.id),
  };

  const product = await ProductModel.create(productData);
  
  res.status(201).json({
    message: 'Product created successfully',
    product
  });
}
