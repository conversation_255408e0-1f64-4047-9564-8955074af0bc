import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { GeminiAI } from '@/lib/ai/gemini';
import { SaleModel } from '@/lib/db/models/Sale';
import { CustomerModel } from '@/lib/db/models/Customer';
import { ObjectId } from 'mongodb';

export default async function handler(req, res) {
  const session = await getServerSession(req, res, authOptions);

  if (!session) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET']);
    return res.status(405).end(`Method ${req.method} Not Allowed`);
  }

  try {
    const { type = 'sales', timeframe = '30d', dealId } = req.query;

    // Calculate date range
    const endDate = new Date();
    const startDate = new Date();
    
    switch (timeframe) {
      case '7d':
        startDate.setDate(endDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(endDate.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(endDate.getDate() - 90);
        break;
      case '1y':
        startDate.setFullYear(endDate.getFullYear() - 1);
        break;
      default:
        startDate.setDate(endDate.getDate() - 30);
    }

    // Build filter based on user role
    const salesFilter = {
      createdAt: { $gte: startDate, $lte: endDate }
    };
    
    const customersFilter = {
      createdAt: { $gte: startDate, $lte: endDate }
    };

    // Non-admin users can only see their own data
    if (session.user.role !== 'admin') {
      salesFilter.assignedTo = new ObjectId(session.user.id);
      customersFilter.assignedTo = new ObjectId(session.user.id);
    }

    let insights;

    switch (type) {
      case 'sales':
        insights = await generateSalesInsights(salesFilter, customersFilter, timeframe);
        break;
      case 'customers':
        insights = await generateCustomerInsights(customersFilter);
        break;
      case 'deal':
        if (!dealId) {
          return res.status(400).json({ message: 'Deal ID required for deal insights' });
        }
        insights = await generateDealInsights(dealId, session);
        break;
      case 'marketing':
        insights = await generateMarketingInsights(salesFilter, customersFilter);
        break;
      case 'forecast':
        insights = await generateForecastInsights(salesFilter, timeframe);
        break;
      default:
        return res.status(400).json({ message: 'Invalid insight type' });
    }

    res.status(200).json({
      type,
      timeframe,
      insights,
      generatedAt: new Date().toISOString(),
    });

  } catch (error) {
    console.error('AI Insights API error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
}

async function generateSalesInsights(salesFilter, customersFilter, timeframe) {
  try {
    const [salesAnalytics, customerStats] = await Promise.all([
      SaleModel.getAnalytics(salesFilter),
      CustomerModel.getStats(customersFilter),
    ]);

    return await GeminiAI.generateSalesInsights(salesAnalytics, customerStats, timeframe);
  } catch (error) {
    console.error('Error generating sales insights:', error);
    throw error;
  }
}

async function generateCustomerInsights(customersFilter) {
  try {
    const customerStats = await CustomerModel.getStats(customersFilter);
    return await GeminiAI.generateCustomerInsights(customerStats);
  } catch (error) {
    console.error('Error generating customer insights:', error);
    throw error;
  }
}

async function generateDealInsights(dealId, session) {
  try {
    const deal = await SaleModel.findById(dealId);
    
    if (!deal) {
      throw new Error('Deal not found');
    }

    // Check permissions
    if (session.user.role !== 'admin' && 
        deal.assignedTo.toString() !== session.user.id &&
        deal.createdBy.toString() !== session.user.id) {
      throw new Error('Access denied');
    }

    // Calculate days in current stage
    const now = new Date();
    const updatedAt = new Date(deal.updatedAt);
    const daysInStage = Math.floor((now - updatedAt) / (1000 * 60 * 60 * 24));

    const dealData = {
      ...deal,
      daysInStage
    };

    return await GeminiAI.generateDealRecommendations(dealData);
  } catch (error) {
    console.error('Error generating deal insights:', error);
    throw error;
  }
}

async function generateMarketingInsights(salesFilter, customersFilter) {
  try {
    const [salesAnalytics, customerStats] = await Promise.all([
      SaleModel.getAnalytics(salesFilter),
      CustomerModel.getStats(customersFilter),
    ]);

    return await GeminiAI.generateMarketingInsights(salesAnalytics, customerStats);
  } catch (error) {
    console.error('Error generating marketing insights:', error);
    throw error;
  }
}

async function generateForecastInsights(salesFilter, timeframe) {
  try {
    // Get historical data for the past year
    const historicalEndDate = new Date(salesFilter.createdAt.$gte);
    const historicalStartDate = new Date();
    historicalStartDate.setFullYear(historicalEndDate.getFullYear() - 1);

    const historicalFilter = {
      createdAt: { $gte: historicalStartDate, $lte: historicalEndDate }
    };

    const historicalData = await SaleModel.getAnalytics(historicalFilter);
    
    return await GeminiAI.generateForecast(historicalData, timeframe);
  } catch (error) {
    console.error('Error generating forecast insights:', error);
    throw error;
  }
}
