import { sessionManager } from '@/lib/fnxChat/session';
import { GoogleGenerativeAI, HarmCategory, HarmBlockThreshold } from '@google/generative-ai';

const MODEL_NAME = "gemini-1.5-pro-latest";
const API_KEY = process.env.GEMINI_API_KEY;

if (!API_KEY) {
  console.warn("GEMINI_API_KEY is not set. The application will not work correctly.");
}

const genAI = new GoogleGenerativeAI(API_KEY || "DUMMY_KEY");

const tools = [{
  functionDeclarations: [
    {
      name: "detailedTourList",
      description: "Kriterlere göre detaylı tur listesini döner",
      parameters: {
        type: "object",
        properties: { criteria: { type: "string" } },
        required: ["criteria"]
      }
    },
    {
      name: "listTours",
      description: "Kriterlere göre tur listesini döner",
      parameters: {
        type: "object",
        properties: { criteria: { type: "string" } },
        required: ["criteria"]
      }
    },
    {
      name: "getTourDetails",
      description: "<PERSON><PERSON><PERSON> bir turun detayların<PERSON> döner",
      parameters: {
        type: "object",
        properties: { tourId: { type: "string" } },
        required: ["tourId"]
      }
    },
    {
      name: "getWeather",
      description: "Belirli bir lokasyon için hava durumu bilgisini döner",
      parameters: {
        type: "object",
        properties: { location: { type: "string" } },
        required: ["location"]
      }
    },
     {
      name: "searchWeb",
      description: "Verilen sorgu için web araması yapar",
      parameters: {
        type: "object",
        properties: { query: { type: "string" } },
        required: ["query"]
      }
    },
    {
      name: "getTime",
      description: "Belirli bir lokasyon için güncel saat bilgisini döner",
      parameters: {
        type: "object",
        properties: { location: { type: "string" } },
        required: ["location"]
      }
    }
  ]
}];

const toolFunctions = {
  detailedTourList: ({ criteria }) => ({
    tours: [
      { id: 'TR001', name: 'Kapadokya Balon Turu', price: '€150', duration: '4 saat', details: `Detaylı Kapadokya turu: ${criteria}`},
      { id: 'TR002', name: 'Efes Antik Kenti Gezisi', price: '€80', duration: '6 saat', details: `Detaylı Efes turu: ${criteria}` },
    ]
  }),
  listTours: ({ criteria }) => ({ 
      tours: `Şu kriterlere uygun turlar: ${criteria}: Kapadokya, Efes, Pamukkale.` 
  }),
  getTourDetails: ({ tourId }) => ({
    details: `Tur ID ${tourId} için detaylar: Bu harika bir tur.`
  }),
  getWeather: ({ location }) => ({
    weather: `Hava durumu (${location}): Güneşli, 25°C.`
  }),
  searchWeb: ({ query }) => ({
      results: `Web araması (${query}) sonuçları: Türkiye'de gezilecek çok yer var.`
  }),
  getTime: ({ location }) => ({
      time: `Saat (${location}): ${new Date().toLocaleTimeString()}`
  }),
};


export default async function handler(req, res) {
  if (req.method !== 'POST') {
    res.setHeader('Allow', ['POST']);
    return res.status(405).end(`Method ${req.method} Not Allowed`);
  }

  const { message, sessionId } = req.body;

  if (!message || !sessionId) {
    return res.status(400).json({ error: 'Message and sessionId are required' });
  }

  const session = await sessionManager.get(sessionId);
  session.history.push({ role: 'user', parts: [{ text: message }] });

  res.setHeader('Content-Type', 'text/event-stream');
  res.setHeader('Cache-Control', 'no-cache');
  res.setHeader('Connection', 'keep-alive');
  res.flushHeaders();

  const sendEvent = (data) => {
    res.write(`data: ${JSON.stringify(data)}\n\n`);
  };

  try {
    if (!API_KEY) {
        throw new Error("GEMINI_API_KEY is not configured on the server.");
    }
    
    const model = genAI.getGenerativeModel({ model: MODEL_NAME, tools });
    const chat = model.startChat({ history: session.history });
    const result = await chat.sendMessageStream(message);

    let fullBotMessage = "";
    for await (const chunk of result.stream) {
      if (chunk.text) {
        const text = chunk.text();
        sendEvent({ type: 'chunk', content: text });
        fullBotMessage += text;
      } else if (chunk.functionCalls) {
        const calls = chunk.functionCalls();
        sendEvent({ type: 'chunk', content: '...araçlar kullanılıyor...' });
        
        const toolResponses = [];
        for (const call of calls) {
          const toolFunc = toolFunctions[call.name];
          if (toolFunc) {
            const args = call.args;
            const toolResponse = await toolFunc(args);
            toolResponses.push({
                executablePart: {
                    toolCall: call,
                    toolResult: {
                        name: call.name,
                        response: toolResponse
                    }
                }
            });
          }
        }

        const secondResult = await chat.sendMessageStream(toolResponses);
        for await (const secondChunk of secondResult.stream) {
           if (secondChunk.text) {
                const text = secondChunk.text();
                sendEvent({ type: 'chunk', content: text });
                fullBotMessage += text;
           }
        }
      }
    }

    if (fullBotMessage) {
        session.history.push({ role: 'model', parts: [{ text: fullBotMessage }] });
        await sessionManager.save(sessionId, session);
    }

    sendEvent({ type: 'done' });

  } catch (error) {
    console.error('Error during chat processing:', error);
    sendEvent({ type: 'error', content: error.message || 'Sunucuda bir hata oluştu.' });
  } finally {
    res.end();
  }
} 