// Dummy Otel Verisi
const dummyHotels = [
    {
        id: 1,
        name: "Grand Hotel",
        location: "İstanbul",
        price: 150,
        tags: ["city"]
    },
    {
        id: 2,
        name: "Beach Resort",
        location: "Antalya",
        price: 200,
        tags: ["sea"]
    },
    {
        id: 3,
        name: "Mountain Lodge",
        location: "Erzurum",
        price: 120,
        tags: ["mountain"]
    },
    { id: 4, name: "Sunset Inn", location: "Bodrum", price: 180, tags: ["sea"] },
    {
        id: 5,
        name: "City Center Hotel",
        location: "Ankara",
        price: 130,
        tags: ["city"]
    },
    {
        id: 6,
        name: "Riverside Hotel",
        location: "Trabzon",
        price: 90,
        tags: ["river"]
    },
    {
        id: 7,
        name: "Skyline Hotel",
        location: "İzmir",
        price: 160,
        tags: ["sea"]
    },
    {
        id: 8,
        name: "Desert Oasis",
        location: "Şanlıurfa",
        price: 110,
        tags: ["desert"]
    },
    {
        id: 9,
        name: "Forest Lodge",
        location: "Bolu",
        price: 100,
        tags: ["forest"]
    },
    {
        id: 10,
        name: "Coastal Hotel",
        location: "Marmaris",
        price: 90,
        tags: ["sea"]
    }
]

export default async function POST(req, res) {
    //   const { message } = await request.json();
    const { message } = await req.body;

    try {
        // Kullanıcı mesajını analiz ederek filtreleri çıkaracak prompt
        const filterPrompt = `
            Aşağıdaki kullanıcı mesajını analiz et ve filtre kriterlerini JSON formatında döndür.

            Mesaj: "${message}"

            Filtre alanları:
            - near_sea (boolean)
            - price_range ("low", "medium", "high")
            - location (string veya null)

            KURALLAR:
            1. Sadece kullanıcı mesajında belirtilen bilgileri çıkar.
            2. Kullanıcı location (konum) belirtmediyse, "location": null olarak ayarla.
            3. "location" alanı boş ya da tahmini olamaz.
            4. JSON dışında başka metin ekleme.
            5. Örnek gibi ama yalnızca gerçek bilgilerle doldur.

            Örnek:
            {"near_sea": true, "price_range": "low", "location": null}
        `;

        const dtBOP = Date.now();
        // Ollama'ya filtre isteği
        const filterResponse = await fetch("http://127.0.0.1:11434/api/generate", {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify({
                model: "gemma3:1b",
                prompt: filterPrompt,
                stream: false
            })
        })
        let filterData
        try {
            filterData = await filterResponse.json();
        } catch (e) {
            console.warn("Filtre JSON parse hatası:", e)
            filterData = { response: "{}" }
        }
        let filters
        try {
            filters = extractAndParseJSON(filterData.response)
            console.log("filterData:", filters, Date.now() - dtBOP, "ms");
            console.log('location', filters, filters.location)
        } catch (e) {
            console.warn("Filtre JSON parse hatası:", e, filterData)
            filters = {}
        }
        console.log('location filter', filters, filters.location)

        // Otelleri filtrelenebilir hale getir
        let filteredHotels = [...dummyHotels];
        filters = JSON.parse(JSON.stringify(filters))
        console.log('location filter', filters, filters.location, filters.near_sea)

        if (filters.near_sea) {
            filteredHotels = filteredHotels.filter(h => h.tags?.includes("sea"))
        }

        if (filters.price_range === "low") {
            filteredHotels = filteredHotels.filter(h => h.price <= 100)
        } else if (filters.price_range === "medium") {
            filteredHotels = filteredHotels.filter(
                h => h.price > 100 && h.price <= 200
            )
        } else if (filters.price_range === "high") {
            filteredHotels = filteredHotels.filter(h => h.price > 200)
        }

        if (filters.location) {
            console.log('location', filters.location, filteredHotels.map(m => m));
            filteredHotels = filteredHotels.filter(h =>
                h.location.toLowerCase().includes(filters.location.toLowerCase())
            )
        } else {
            console.log('no location', filters)
        }
 
        const chatPrompt = `
            Sadece aşağıdaki otel listesinden bahsetmelisin. Dış veritabanı veya başka bilgilerden bilgi kullanma.

            Kullanıcının sorgusu: "${message}"

            Seçilen Oteller:
            ${filteredHotels.map(hotel =>
            `• ${hotel.name} - ${hotel.location}, Fiyat: ${hotel.price} TL, Özellikler: ${hotel.tags.join(', ')}`
        ).join('\n')}

            Yanıt kuralları:
            1. Sadece yukarıdaki listeden otel öner.
            2. Neden bu otelleri seçtiğini açıkla (fiyat, konum gibi kriterlere göre).
            3. Kullanıcıya dostane ve yardımcı bir dille cevap ver.
            4. Ekstra bilgi ekleme, sadece gerekli olanı söyle.

            Toplamda ${filteredHotels.length} otel bulundu.
        `;

        console.log("chatPrompt:", chatPrompt, Date.now() - dtBOP, "ms");
        // Chat cevabını al
        const chatResponse = await fetch("http://127.0.0.1:11434/api/generate", {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify({
                // model: "llama3.1:8b",
                model: "gemma3:1b",
                prompt: chatPrompt,
                stream: false
            })
        })

        const chatData = await chatResponse.json()
        console.log("chatData:", chatData.response, Date.now() - dtBOP, "ms");
        res.status(200).json({
            reply:
                chatData.response ||
                "Belirttiğiniz kriterlere uygun otelleri listeledim.",
            hotels: filteredHotels
        })
    } catch (error) {
        console.error("API Hatası:", error)
        return new Response(
            JSON.stringify({ reply: "Bir hata oluştu.", hotels: [] }),
            { status: 500, headers: { "Content-Type": "application/json" } }
        )
    }
}


function extractAndParseJSON(responseText) {
    try {
        // Markdown kod bloğu işaretlerini (`json` ve ``` işaretlerini) temizleyin
        const jsonRegex = /```json\s*([\s\S]*?)\s*```/; // JSON içeriğini bulmak için regex
        const match = responseText.match(jsonRegex);

        if (!match || !match[1]) {
            throw new Error("Yanıtta geçerli bir JSON bulunamadı.");
        }

        const jsonString = match[1].trim(); // JSON içeriğini al

        // JSON'ı parse edin
        const parsedJSON = JSON.parse(jsonString);
        return parsedJSON;
    } catch (error) {
        console.error("JSON extraction veya parsing hatası:", error.message);
        try {
            let jsonString = responseText;
            const parsedJSON = JSON.parse(jsonString);
            return parsedJSON;
        } catch (e) {
            console.error("JSON extraction veya parsing hatası:", e.message);
            // throw error;
            return responseText;
        }
    }
}
