import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { SaleModel } from '@/lib/db/models/Sale';
import { ObjectId } from 'mongodb';

export default async function handler(req, res) {
  const session = await getServerSession(req, res, authOptions);

  if (!session) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  const { method } = req;

  try {
    switch (method) {
      case 'GET':
        await handleGet(req, res, session);
        break;
      case 'POST':
        await handlePost(req, res, session);
        break;
      default:
        res.setHeader('Allow', ['GET', 'POST']);
        res.status(405).end(`Method ${method} Not Allowed`);
    }
  } catch (error) {
    console.error('Sales API error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
}

async function handleGet(req, res, session) {
  const { 
    page = 1, 
    limit = 10, 
    stage, 
    assignedTo, 
    status = 'active',
    search,
    sortBy = 'createdAt',
    sortOrder = 'desc'
  } = req.query;

  // Build filter
  const filter = { status };
  
  if (stage) {
    filter.stage = stage;
  }
  
  if (assignedTo) {
    filter.assignedTo = new ObjectId(assignedTo);
  }

  // Non-admin users can only see their own sales
  if (session.user.role !== 'admin') {
    filter.assignedTo = new ObjectId(session.user.id);
  }

  // Build sort object
  const sort = {};
  sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

  const options = {
    page: parseInt(page),
    limit: parseInt(limit),
    sort,
    search
  };

  const result = await SaleModel.findAll(filter, options);
  
  res.status(200).json(result);
}

async function handlePost(req, res, session) {
  const {
    title,
    description,
    customerId,
    productIds,
    assignedTo,
    stage = 'lead',
    value,
    currency = 'USD',
    probability = 0,
    expectedCloseDate,
    source,
    priority = 'medium',
    tags = []
  } = req.body;

  // Validation
  if (!title || !customerId) {
    return res.status(400).json({ 
      message: 'Title and customer are required' 
    });
  }

  const saleData = {
    title,
    description,
    customerId: new ObjectId(customerId),
    productIds: productIds ? productIds.map(id => new ObjectId(id)) : [],
    assignedTo: assignedTo ? new ObjectId(assignedTo) : new ObjectId(session.user.id),
    createdBy: new ObjectId(session.user.id),
    stage,
    value: parseFloat(value) || 0,
    currency,
    probability: parseInt(probability),
    expectedCloseDate: expectedCloseDate ? new Date(expectedCloseDate) : null,
    source,
    priority,
    tags
  };

  const sale = await SaleModel.create(saleData);
  
  res.status(201).json({
    message: 'Sale created successfully',
    sale
  });
}
