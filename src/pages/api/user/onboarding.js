import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { UserModel } from '@/lib/db/models/User';
import clientPromise from "@/lib/db/mongodb";

export default async function handler(req, res) {
  const session = await getServerSession(req, res, authOptions);
  console.log('onboarding session:', session);
  if (!session) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  if (req.method !== 'POST') {
    res.setHeader('Allow', ['POST']);
    return res.status(405).end(`Method ${req.method} Not Allowed`);
  }

  try {
    const { profile, preferences, goals } = req.body;

    // Update user profile with onboarding data
    const updateData = {
      onboardingCompleted: true,
      profile: profile || {},
      preferences: preferences || {},
      goals: goals || {},
    };

    const result = await UserModel.update(session.user.id, updateData);

    if (result.matchedCount === 0) {
      return res.status(404).json({ message: 'User not found' });
    }

    const client = await clientPromise;
    const userscollection = 'tourai.main.dim.users';
    const db = client.db('toursdb'); // or your db name
    await db.collection(userscollection).updateOne(
      { email: session.user.email },
      { $set: { onboardingCompleted: true } }
    );

    res.status(200).json({
      message: 'Onboarding completed successfully',
      user: {
        id: session.user.id,
        onboardingCompleted: true,
      }
    });

  } catch (error) {
    console.error('Onboarding API error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
}
