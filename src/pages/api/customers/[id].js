import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { CustomerModel } from '@/lib/db/models/Customer';
import { ObjectId } from 'mongodb';

export default async function handler(req, res) {
  const session = await getServerSession(req, res, authOptions);

  if (!session) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  const { method } = req;
  const { id } = req.query;

  if (!ObjectId.isValid(id)) {
    return res.status(400).json({ message: 'Invalid customer ID' });
  }

  try {
    switch (method) {
      case 'GET':
        await handleGet(req, res, session, id);
        break;
      case 'PUT':
        await handlePut(req, res, session, id);
        break;
      case 'DELETE':
        await handleDelete(req, res, session, id);
        break;
      default:
        res.setHeader('Allow', ['GET', 'PUT', 'DELETE']);
        res.status(405).end(`Method ${method} Not Allowed`);
    }
  } catch (error) {
    console.error('Customer API error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
}

async function handleGet(req, res, session, id) {
  const customer = await CustomerModel.findById(id);

  if (!customer) {
    return res.status(404).json({ message: 'Customer not found' });
  }

  // Check permissions
  if (session.user.role !== 'admin' && 
      customer.assignedTo.toString() !== session.user.id &&
      customer.createdBy.toString() !== session.user.id) {
    return res.status(403).json({ message: 'Access denied' });
  }

  res.status(200).json(customer);
}

async function handlePut(req, res, session, id) {
  const customer = await CustomerModel.findById(id);

  if (!customer) {
    return res.status(404).json({ message: 'Customer not found' });
  }

  // Check permissions
  if (session.user.role !== 'admin' && 
      customer.assignedTo.toString() !== session.user.id &&
      customer.createdBy.toString() !== session.user.id) {
    return res.status(403).json({ message: 'Access denied' });
  }

  const updateData = { ...req.body };
  
  // Convert string IDs to ObjectIds
  if (updateData.assignedTo) {
    updateData.assignedTo = new ObjectId(updateData.assignedTo);
  }

  // Remove fields that shouldn't be updated directly
  delete updateData._id;
  delete updateData.createdAt;
  delete updateData.createdBy;

  const result = await CustomerModel.update(id, updateData);

  if (result.matchedCount === 0) {
    return res.status(404).json({ message: 'Customer not found' });
  }

  const updatedCustomer = await CustomerModel.findById(id);
  
  res.status(200).json({
    message: 'Customer updated successfully',
    customer: updatedCustomer
  });
}

async function handleDelete(req, res, session, id) {
  const customer = await CustomerModel.findById(id);

  if (!customer) {
    return res.status(404).json({ message: 'Customer not found' });
  }

  // Check permissions - only admin or creator can delete
  if (session.user.role !== 'admin' && 
      customer.createdBy.toString() !== session.user.id) {
    return res.status(403).json({ message: 'Access denied' });
  }

  const result = await CustomerModel.delete(id);

  if (result.deletedCount === 0) {
    return res.status(404).json({ message: 'Customer not found' });
  }

  res.status(200).json({ message: 'Customer deleted successfully' });
}
