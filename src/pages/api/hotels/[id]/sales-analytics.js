import { MongoClient } from 'mongodb';

const uri = process.env.MONGODB_TOUR_URI;
const client = new MongoClient(uri);

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  const { id } = req.query;

  try {
    await client.connect();
    const db = client.db('toursdb');
    const collection = db.collection('tourai.data.fact.sales_hotelsdomestic');

    const currentYear = new Date().getFullYear();
    const currentMonth = new Date().getMonth() + 1;
    const previousYear = currentYear - 1;

    // Current year data (monthly aggregation)
    const currentYearData = await collection.aggregate([
      {
        $match: {
          TESIS_ID: parseInt(id),
          SATIS_DONEM: {
            $regex: `^${currentYear}-`
          }
        }
      },
      {
        $group: {
          _id: "$SATIS_DONEM",
          totalRevenue: { $sum: "$tTutar" },
          totalReservations: { $sum: "$REZV" },
          totalGuests: { $sum: { $add: ["$nYETISKIN", "$nCocuk"] } },
          totalCost: { $sum: "$tMaliyet" }
        }
      },
      { $sort: { _id: 1 } }
    ]).toArray();

    // Previous year data (same period)
    const previousYearData = await collection.aggregate([
      {
        $match: {
          TESIS_ID: parseInt(id),
          SATIS_DONEM: {
            $regex: `^${previousYear}-`
          }
        }
      },
      {
        $group: {
          _id: "$SATIS_DONEM",
          totalRevenue: { $sum: "$tTutar" },
          totalReservations: { $sum: "$REZV" },
          totalGuests: { $sum: { $add: ["$nYETISKIN", "$nCocuk"] } },
          totalCost: { $sum: "$tMaliyet" }
        }
      },
      { $sort: { _id: 1 } }
    ]).toArray();

    // Current year to date (up to current month)
    const currentYearToDate = await collection.aggregate([
      {
        $match: {
          TESIS_ID: parseInt(id),
          SATIS_DONEM: {
            $regex: `^${currentYear}-(0[1-9]|1[0-${currentMonth.toString().padStart(2, '0').charAt(1)}])$`
          }
        }
      },
      {
        $group: {
          _id: null,
          totalRevenue: { $sum: "$tTutar" },
          totalReservations: { $sum: "$REZV" },
          totalGuests: { $sum: { $add: ["$nYETISKIN", "$nCocuk"] } },
          totalCost: { $sum: "$tMaliyet" }
        }
      }
    ]).toArray();

    // Sales channel trend (last 12 months)
    const salesChannelTrend = await collection.aggregate([
      {
        $match: {
          TESIS_ID: parseInt(id),
          SATIS_DONEM: {
            $gte: `${currentYear - 1}-${currentMonth.toString().padStart(2, '0')}`,
            $lte: `${currentYear}-${currentMonth.toString().padStart(2, '0')}`
          }
        }
      },
      {
        $group: {
          _id: {
            period: "$SATIS_DONEM",
            channel: "$SATIS_KANALI"
          },
          totalRevenue: { $sum: "$tTutar" },
          totalReservations: { $sum: "$REZV" }
        }
      },
      {
        $group: {
          _id: "$_id.period",
          channels: {
            $push: {
              channel: "$_id.channel",
              revenue: "$totalRevenue",
              reservations: "$totalReservations"
            }
          }
        }
      },
      { $sort: { _id: 1 } }
    ]).toArray();

    // Room type trend (last 12 months)
    const roomTypeTrend = await collection.aggregate([
      {
        $match: {
          TESIS_ID: parseInt(id),
          SATIS_DONEM: {
            $gte: `${currentYear - 1}-${currentMonth.toString().padStart(2, '0')}`,
            $lte: `${currentYear}-${currentMonth.toString().padStart(2, '0')}`
          }
        }
      },
      {
        $group: {
          _id: {
            period: "$SATIS_DONEM",
            roomType: "$TESIS_ODA_TIP"
          },
          totalReservations: { $sum: "$REZV" }
        }
      },
      {
        $group: {
          _id: "$_id.period",
          roomTypes: {
            $push: {
              roomType: "$_id.roomType",
              reservations: "$totalReservations"
            }
          }
        }
      },
      { $sort: { _id: 1 } }
    ]).toArray();

    // Bayi distribution (current year)
    const bayiDistribution = await collection.aggregate([
      {
        $match: {
          TESIS_ID: parseInt(id),
          SATIS_DONEM: {
            $regex: `^${currentYear}-`
          }
        }
      },
      {
        $group: {
          _id: "$BAYI_ADI",
          totalRevenue: { $sum: "$tTutar" },
          totalReservations: { $sum: "$REZV" }
        }
      },
      { $sort: { totalRevenue: -1 } },
      { $limit: 10 }
    ]).toArray();

    // Current month bayi distribution
    const currentMonthBayiDistribution = await collection.aggregate([
      {
        $match: {
          TESIS_ID: parseInt(id),
          SATIS_DONEM: `${currentYear}-${currentMonth.toString().padStart(2, '0')}`
        }
      },
      {
        $group: {
          _id: "$BAYI_ADI",
          totalRevenue: { $sum: "$tTutar" },
          totalReservations: { $sum: "$REZV" }
        }
      },
      { $sort: { totalRevenue: -1 } },
      { $limit: 10 }
    ]).toArray();

    res.status(200).json({
      success: true,
      data: {
        currentYear: currentYearData,
        previousYear: previousYearData,
        currentYearToDate: currentYearToDate[0] || {
          totalRevenue: 0,
          totalReservations: 0,
          totalGuests: 0,
          totalCost: 0
        },
        salesChannelTrend,
        roomTypeTrend,
        bayiDistribution,
        currentMonthBayiDistribution
      }
    });

  } catch (error) {
    console.error('Sales analytics error:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error',
      error: error.message 
    });
  } finally {
    await client.close();
  }
}
