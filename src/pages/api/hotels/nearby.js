import { getCollection } from "@/lib/db/mongodb";

export default async function handler(req, res) {
    const collection = await getCollection("tourai.data.dim.hotelsdomestic");
    const { lat, lng, radius = 1, id } = req.query;
    if (!lat || !lng) {
        return res.status(400).json({ error: 'lat and lng required' });
    }
    let q = {};
    try {
        q = {
            TESIS_ID: { $ne: parseInt(id) },
            location: {
                $near: {
                    $geometry: { type: "Point", coordinates: [parseFloat(lng), parseFloat(lat)] },
                    $maxDistance: Number(radius) * 1000 // metre cinsinden
                }
            }
        };
        // Sonuçları diziye çevir!
        const hotels = await collection
          .find(q)
          .project({ TESIS_ID: 1, TESIS_ADI: 1, SEO_URL: 1, LATITUDE: 1, LONGITUDE: 1 })
          .limit(20)
          .toArray();
        res.status(200).json({ data: hotels });
    } catch (e) {
        res.status(500).json({ error: 'Server error', detail: e.message, query: q });
    }
}