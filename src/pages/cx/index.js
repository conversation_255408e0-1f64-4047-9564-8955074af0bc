import React, { useState, useEffect, useRef } from 'react'; // Import useRef
import Link from 'next/link';
import { cn } from '../../lib/utils';
import { Button } from '@/components/ui/button';
import { Avatar } from '@/components/ui/avatar';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Bell, Mail, Menu, GripHorizontal } from 'lucide-react'; // Import GripHorizontal
import Sidebar from '@/components/Sidebar';

const AdminDashboard = () => {
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [leftPanelWidth, setLeftPanelWidth] = useState(30); // Initial width in percentage
  const [isResizing, setIsResizing] = useState(false);
  const containerRef = useRef(null); // Ref for the main content area

  const MIN_LEFT_PANEL_WIDTH_PX = 300; // Minimum width for the left panel in pixels
  const MIN_RIGHT_PANEL_WIDTH_PX = 500; // Minimum width for the right panel in pixels

  const toggleDrawer = () => {
    setIsDrawerOpen(!isDrawerOpen);
  };

  const handleMouseDown = (e) => {
    setIsResizing(true);
  };

  const handleMouseMove = (e) => {
    if (!isResizing) return;

    if (containerRef.current) {
      const containerRect = containerRef.current.getBoundingClientRect();
      let newWidthPx = e.clientX - containerRect.left;

      // Calculate max width for left panel based on right panel's min width
      const maxLeftPanelWidthPx = containerRect.width - MIN_RIGHT_PANEL_WIDTH_PX;

      // Clamp newWidthPx between min and max
      newWidthPx = Math.max(MIN_LEFT_PANEL_WIDTH_PX, Math.min(newWidthPx, maxLeftPanelWidthPx));

      // Convert pixel width back to percentage relative to the container's current width
      const newWidthPercent = (newWidthPx / containerRect.width) * 100;
      setLeftPanelWidth(newWidthPercent);
    }
  };

  const handleMouseUp = () => {
    setIsResizing(false);
  };

  useEffect(() => {
    if (isResizing) {
      window.addEventListener('mousemove', handleMouseMove);
      window.addEventListener('mouseup', handleMouseUp);
    } else {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
    }

    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isResizing]);

  return (
    <div className="h-screen max-w-[2048px] mx-auto bg-gray-100">
      <div className="flex h-full min-w-[600px] overflow-x-auto">
        {/* Sidebar Component */}
        <Sidebar isDrawerOpen={isDrawerOpen} toggleDrawer={toggleDrawer} />

        {/* Main Content Area */}
        <div className="flex flex-col flex-1 overflow-hidden">
          {/* Sticky Header */}
          <header className="flex items-center justify-between h-16 px-6 bg-white border-b border-gray-200 sticky top-0 z-10">
            <div className="flex items-center">
              <Button variant="ghost" size="icon" onClick={toggleDrawer} className="mr-4 md:hidden">
                <Menu className="h-6 w-6" />
              </Button>
              <div className="flex flex-col">
                <h2 className="text-2xl font-bold text-gray-800 whitespace-nowrap truncate">Dashboard Overview</h2>
                <nav className="text-sm text-gray-500">
                  <ol className="flex list-none p-0 m-0">
                    <li className="flex items-center">
                      <Link href="/cx" className="hover:underline">Home</Link>
                      <span className="mx-2">/</span>
                    </li>
                    <li className="text-gray-700">Dashboard</li>
                  </ol>
                </nav>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Button variant="outline">New Report</Button>
              <Button variant="default">Add User</Button>
              <Button variant="ghost" size="icon">
                <Bell className="h-5 w-5" />
              </Button>
              <Button variant="ghost" size="icon">
                <Mail className="h-5 w-5" />
              </Button>
              <Avatar className="h-8 w-8">
                {/* Placeholder for user avatar */}
                <span className="flex h-full w-full items-center justify-center rounded-full bg-gray-300 text-sm">U</span>
              </Avatar>
            </div>
          </header>

          {/* Content Area - Split into two panels */}
          <main ref={containerRef} className="flex flex-1 overflow-hidden"> {/* Removed p-6, added flex and overflow-hidden */}
            {/* Left Panel */}
            <div style={{ width: `${leftPanelWidth}%` }} className="flex flex-col flex-shrink-0 p-6 overflow-y-auto bg-white border-r border-gray-200">
              <h3 className="text-xl font-semibold mb-4">Static Left Panel</h3>
              <p>This panel is static and will not scroll vertically.</p>
              <p>It has a minimum width of {MIN_LEFT_PANEL_WIDTH_PX}px.</p>
              <div className="mt-4 space-y-2">
                <Card>
                  <CardHeader><CardTitle>Left Card 1</CardTitle></CardHeader>
                  <CardContent><p>Content for left card 1.</p></CardContent>
                </Card>
                <Card>
                  <CardHeader><CardTitle>Left Card 2</CardTitle></CardHeader>
                  <CardContent><p>Content for left card 2.</p></CardContent>
                </Card>
                <Card>
                  <CardHeader><CardTitle>Left Card 3</CardTitle></CardHeader>
                  <CardContent><p>Content for left card 3.</p></CardContent>
                </Card>
                <Card>
                  <CardHeader><CardTitle>Left Card 4</CardTitle></CardHeader>
                  <CardContent><p>Content for left card 4.</p></CardContent>
                </Card>
              </div>
            </div>

            {/* Resizer Bar */}
            <div
              className="w-1 bg-gray-300 cursor-ew-resize flex items-center justify-center"
              onMouseDown={handleMouseDown}
            >
              <GripHorizontal className="h-[50px] w-5 text-gray-800" />
            </div>

            {/* Right Panel */}
            <div className="flex-1 p-6 overflow-y-auto">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Total Users</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-4xl font-bold">1,234</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader>
                    <CardTitle>Active Sessions</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-4xl font-bold">567</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader>
                    <CardTitle>Revenue</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-4xl font-bold">$89,012</p>
                  </CardContent>
                </Card>
              </div>

              <div className="mt-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Recent Activity</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      <li>User &apos;John Doe&apos; logged in.</li>
                      <li>New report &apos;Sales Q1&apos; generated.</li>
                      <li>Settings updated by &apos;Admin&apos;.</li>
                      <li>User &apos;Jane Smith&apos; registered.</li>
                      <li>User &apos;John Doe&apos; logged in.</li>
                      <li>New report &apos;Sales Q1&apos; generated.</li>
                      <li>Settings updated by &apos;Admin&apos;.</li>
                      <li>User &apos;Jane Smith&apos; registered.</li>
                      <li>User &apos;John Doe&apos; logged in.</li>
                      <li>New report &apos;Sales Q1&apos; generated.</li>
                      <li>Settings updated by &apos;Admin&apos;.</li>
                      <li>User &apos;Jane Smith&apos; registered.</li>
                      <li>User &apos;John Doe&apos; logged in.</li>
                      <li>New report &apos;Sales Q1&apos; generated.</li>
                      <li>Settings updated by &apos;Admin&apos;.</li>
                      <li>User &apos;Jane Smith&apos; registered.
                      </li><li>User &apos;John Doe&apos; logged in.</li>
                      <li>New report &apos;Sales Q1&apos; generated.</li>
                      <li>Settings updated by &apos;Admin&apos;.</li>
                      <li>User &apos;Jane Smith&apos; registered.</li>
                      <li>User &apos;John Doe&apos; logged in.</li>
                      <li>New report &apos;Sales Q1&apos; generated.</li>
                      <li>Settings updated by &apos;Admin&apos;.</li>
                      <li>User &apos;Jane Smith&apos; registered.</li>
                      <li>User &apos;John Doe&apos; logged in.</li>
                      <li>New report &apos;Sales Q1&apos; generated.</li>
                      <li>Settings updated by &apos;Admin&apos;.</li>
                      <li>User &apos;Jane Smith&apos; registered.</li>
                      <li>User &apos;John Doe&apos; logged in.</li>
                      <li>New report &apos;Sales Q1&apos; generated.</li>
                      <li>Settings updated by &apos;Admin&apos;.</li>
                      <li>User &apos;Jane Smith&apos; registered.</li>
                    </ul>
                  </CardContent>
                </Card>
              </div>
            </div>
          </main>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;