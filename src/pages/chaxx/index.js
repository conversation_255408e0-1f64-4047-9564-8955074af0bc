import { useState, useEffect } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { Sidebar } from '@/components/Sidebar';
import { MainContent } from '@/components/MainContent';

export default function Home() {
    const [messages, setMessages] = useState([]);
    const [input, setInput] = useState('');
    const [sessionId, setSessionId] = useState(null);
    const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);

    const toggleSidebar = () => {
        setIsSidebarCollapsed(!isSidebarCollapsed);
    };

    // Initialize session and load messages
    useEffect(() => {
        let storedSessionId = localStorage.getItem('chatSessionId');
        if (!storedSessionId) {
            storedSessionId = uuidv4();
            localStorage.setItem('chatSessionId', storedSessionId);
        }
        setSessionId(storedSessionId);

        const initialMessages = [
            { id: 'initial', role: 'assistant', content: '' } // Empty initial message to show suggestions
        ];

        try {
            const storedMessages = localStorage.getItem(`chatMessages_${storedSessionId}`);
            const parsedMessages = storedMessages ? JSON.parse(storedMessages) : initialMessages;
            setMessages(parsedMessages.length > 0 ? parsedMessages : initialMessages);
        } catch (e) {
            setMessages(initialMessages);
        }
    }, []);

    // Save messages to localStorage
    useEffect(() => {
        if (sessionId && messages.length > 0) {
            // Don't save the initial empty message
            if (messages.length === 1 && messages[0].id === 'initial') return;
            localStorage.setItem(`chatMessages_${sessionId}`, JSON.stringify(messages));
        }
    }, [messages, sessionId]);

    const handleInputChange = (e) => {
        setInput(e.target.value);
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        if (!input.trim() || !sessionId) return;

        // Clear initial empty message if it exists
        if (messages.length === 1 && messages[0].id === 'initial') {
            setMessages([]);
        }

        const userMessage = { id: uuidv4(), role: 'user', content: input };
        setMessages(prev => [...prev, userMessage]);
        const currentInput = input;
        setInput('');

        const assistantMessageId = uuidv4();
        setMessages(prev => [...prev, { id: assistantMessageId, role: 'assistant', content: '' }]);

        try {
            const response = await fetch('/api/chat/msg', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ message: currentInput, sessionId }),
            });

            if (!response.body) return;
            const reader = response.body.pipeThrough(new TextDecoderStream()).getReader();
            let buffer = '';

            while (true) {
                const { value, done } = await reader.read();
                if (done) break;
                buffer += value;
                const parts = buffer.split('\\n\\n');
                buffer = parts.pop() || '';

                for (const part of parts) {
                    if (part.startsWith('data: ')) {
                        const dataStr = part.substring(6);
                        try {
                            const data = JSON.parse(dataStr);
                            if (data.type === 'chunk') {
                                setMessages(prev => prev.map(msg =>
                                    msg.id === assistantMessageId
                                        ? { ...msg, content: msg.content + data.content }
                                        : msg
                                ));
                            } else if (data.type === 'error') {
                                setMessages(prev => prev.map(msg =>
                                    msg.id === assistantMessageId
                                        ? { ...msg, content: 'Hata: ' + data.content }
                                        : msg
                                ));
                            }
                        } catch (e) {
                            console.error('Failed to parse stream data:', e);
                        }
                    }
                }
            }
        } catch (error) {
            console.error("Failed to send message:", error);
            setMessages(prev => prev.map(msg =>
                msg.id === assistantMessageId
                    ? { ...msg, content: 'Üzgünüm, bir hata oluştu. Lütfen tekrar deneyin.' }
                    : msg
            ));
        }
    };

    return (
        <div className="flex h-screen bg-background text-foreground font-sans">
            <Sidebar isCollapsed={isSidebarCollapsed} toggleSidebar={toggleSidebar} />
            <MainContent
                messages={messages}
                input={input}
                onInputChange={handleInputChange}
                onSubmit={handleSubmit}
                isSidebarCollapsed={isSidebarCollapsed}
            />
        </div>
    );
}
