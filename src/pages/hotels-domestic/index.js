import React, { useState, useEffect, useRef } from 'react';
import { useSession } from 'next-auth/react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { GripHorizontal, Send, MessageCircle, Bot, User } from 'lucide-react';
import ProtectedRoute from '@/components/ProtectedRoute';
import Layout from '@/components/layouts/Layout';
import { Button } from '@/components/ui/button';
import { Avatar } from '@/components/ui/avatar';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Bell, Mail, Menu, Building } from 'lucide-react';
import {TopPerformingHotelsTabz} from '@/components/pagecomponents/TopPerformingHotelsTabs';
import {
 BarChart,
 Bar,
 XAxis,
 YAxis,
 CartesianGrid,
 Tooltip,
 ResponsiveContainer,
} from 'recharts';

const AdminDashboard = () => {
  const { data: session } = useSession();
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [leftPanelWidth, setLeftPanelWidth] = useState(30);
  const [isResizing, setIsResizing] = useState(false);
  const [hotelBooksStg, setHotelBooksStg] = useState(null);
  const [hotelBooksStgLoading, setHotelBooksStgLoading] = useState(true);
  const [monthlyTrendsData, setMonthlyTrendsData] = useState([]);
  const [monthlyTrendsLoading, setMonthlyTrendsLoading] = useState(true);
  const containerRef = useRef(null);

  // Chat state
  const [messages, setMessages] = useState([
    {
      id: 1,
      text: "Merhaba! Size nasıl yardımcı olabilirim?",
      sender: "bot",
      timestamp: new Date().toLocaleTimeString()
    }
  ]);
  const [newMessage, setNewMessage] = useState("");
  const chatEndRef = useRef(null);

  const MIN_LEFT_PANEL_WIDTH_PX = 300;
  const currentYear = new Date().getFullYear();
  const previousYear = currentYear - 1;
  const MIN_RIGHT_PANEL_WIDTH_PX = 500;

  const toggleDrawer = () => setIsDrawerOpen(!isDrawerOpen);

  const setHotelBooks = (data) => {
    setHotelBooksStg(data);
  }
  const setHotelBooksLoading = (loading) => {
    setHotelBooksStgLoading(loading);
  }

  // Fetch monthly trends data
  useEffect(() => {
    const fetchMonthlyTrends = async () => {
      setMonthlyTrendsLoading(true);
      try {
        const response = await fetch('/api/hotels/stats?w=ayliksatislarverezervasyon');
        if (response.ok) {
          const data = await response.json();
          setMonthlyTrendsData(data.data || []);
        } else {
          console.error('Failed to fetch monthly trends:', response.statusText);
        }
      } catch (error) {
        console.error('Error fetching monthly trends:', error);
      } finally {
        setMonthlyTrendsLoading(false);
      }
    };
    fetchMonthlyTrends();
  }, []);

  // Chat functions
  const sendMessagex = () => {
    if (newMessage.trim()) {
      const userMessage = {
        id: messages.length + 1,
        text: newMessage,
        sender: "user",
        timestamp: new Date().toLocaleTimeString()
      };

      setMessages(prev => [...prev, userMessage]);
      setNewMessage("");

      // Simulate bot response
      setTimeout(() => {
        const botMessage = {
          id: messages.length + 2,
          text: "Mesajınızı aldım. Size yardımcı olmaya çalışıyorum...",
          sender: "bot",
          timestamp: new Date().toLocaleTimeString()
        };
        setMessages(prev => [...prev, botMessage]);
      }, 1000);
    }
  };

  // ... existing code ...

  const sendMessage = async () => {
    if (newMessage.trim()) {
      const userMessage = {
        id: messages.length + 1,
        text: newMessage,
        sender: "user",
        timestamp: new Date().toLocaleTimeString()
      };

      setMessages(prev => [...prev, userMessage]);
      setNewMessage("");

      try {
        const response = await fetch('/api/chat/domestic', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ message: newMessage }),
        });

        const data = await response.json();
        console.log("data response:", data);
        const botMessage = {
          id: messages.length + 2,
          text: data.reply,
          sender: "bot",
          timestamp: new Date().toLocaleTimeString()
        };
        setMessages(prev => [...prev, botMessage]);

      } catch (error) {
          console.error("Error sending message:", error);
          const botMessage = {
              id: messages.length + 2,
              text: "Bir hata oluştu. Lütfen tekrar deneyiniz.",
              sender: "bot",
              timestamp: new Date().toLocaleTimeString()
          };
          setMessages(prev => [...prev, botMessage]);
      }
    }
  };

// ... rest of code ...
  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      sendMessage();
    }
  };

  // Scroll to bottom when new message is added
  useEffect(() => {
    chatEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  const handleMouseDown = () => setIsResizing(true);

  const handleMouseMove = (e) => {
    if (!isResizing) return;
    if (containerRef.current) {
      const containerRect = containerRef.current.getBoundingClientRect();
      let newWidthPx = e.clientX - containerRect.left;
      const maxLeftPanelWidthPx = containerRect.width - MIN_RIGHT_PANEL_WIDTH_PX;
      newWidthPx = Math.max(MIN_LEFT_PANEL_WIDTH_PX, Math.min(newWidthPx, maxLeftPanelWidthPx));
      const newWidthPercent = (newWidthPx / containerRect.width) * 100;
      setLeftPanelWidth(newWidthPercent);
    }
  };

  const handleMouseUp = () => setIsResizing(false);

  useEffect(() => {
    if (isResizing) {
      window.addEventListener('mousemove', handleMouseMove);
      window.addEventListener('mouseup', handleMouseUp);
    } else {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
    }
    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isResizing]);

  const TopRightIcons = () => ( 
    <div className="flex items-center space-x-4">
      <Button variant="outline">New Report</Button>
      <Button variant="default">Add User</Button>
      <Button variant="ghost" size="icon">
        <Bell className="h-5 w-5" />
      </Button>
      <Button variant="ghost" size="icon">
        <Mail className="h-5 w-5" />
      </Button>
      <Avatar className="h-8 w-8">
        {/* Placeholder for user avatar */}
        <span className="flex h-full w-full items-center justify-center rounded-full bg-gray-300 text-sm">U</span>
      </Avatar>
    </div>
  )

  return (
    <ProtectedRoute>
      <Layout isDrawerOpen={isDrawerOpen}
        toggleDrawer={toggleDrawer}
        title={'Hotels - Domestic'}
        breadCrumbs={['Home', 'Hotels - Domestic']}
      // topRightIcons={<TopRightIcons />} 
      >

        <div className="space-y-1">
          {/* Welcome Section */}
          <div className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-6 text-white">
            <h1 className="text-2xl font-bold mb-2">
              Yurtiçi Tesisler
            </h1>
            <p className="text-blue-100">
              (+Kıbrıs/Mısır) Temel göstergeler ve tesis listesi.
            </p>
          </div>

          <main ref={containerRef} className="flex flex-1">
            {/* Left Panel - Chat Area - Sticky */}
            <div style={{ width: `${leftPanelWidth}%` }} className="flex flex-col flex-shrink-0 bg-white border-r border-gray-200 h-[calc(100vh-120px)] sticky top-4">
              {/* Chat Header */}

              <Button className="m-4" variant="outline" onClick={() => console.log('tıklandı')}>
                <Building className="h-4 w-4 mr-2" />
                    <a
                      href={`/hotels-domestic/list`}
                      rel="noopener noreferrer"
                      className="flex items-center text-blue-600 hover:underline"
                    >
                      Tesis Listesi
                    </a>

              </Button>


              <Card className="m-4 mb-0 rounded-b-none border-b-0">
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center gap-2">
                    <MessageCircle className="h-5 w-5 text-blue-600" />
                    Yoda
                  </CardTitle>
                </CardHeader>
              </Card>

              {/* Chat Messages Area */}
              <div className="flex-1 mx-4 mb-0 overflow-hidden">
                <Card className="h-full rounded-t-none rounded-b-none border-t-0 border-b-0">
                  <CardContent className="p-0 h-full">
                    <ScrollArea className="h-full p-4">
                      <div className="space-y-4">
                        {messages.map((message) => (
                          <div
                            key={message.id}
                            className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
                          >
                            <div className={`flex items-start gap-2 max-w-[80%] ${message.sender === 'user' ? 'flex-row-reverse' : 'flex-row'}`}>
                              <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
                                message.sender === 'user'
                                  ? 'bg-blue-600 text-white'
                                  : 'bg-gray-200 text-gray-600'
                              }`}>
                                {message.sender === 'user' ? (
                                  <User className="h-4 w-4" />
                                ) : (
                                  <Bot className="h-4 w-4" />
                                )}
                              </div>
                              <div className={`rounded-lg p-3 ${
                                message.sender === 'user'
                                  ? 'bg-blue-600 text-white'
                                  : 'bg-gray-100 text-gray-800'
                              }`}>
                                <p className="text-sm">{message.text}</p>
                                <p className={`text-xs mt-1 ${
                                  message.sender === 'user' ? 'text-blue-100' : 'text-gray-500'
                                }`}>
                                  {message.timestamp}
                                </p>
                              </div>
                            </div>
                          </div>
                        ))}
                        <div ref={chatEndRef} />
                      </div>
                    </ScrollArea>
                  </CardContent>
                </Card>
              </div>

              {/* Chat Input Area */}
              <Card className="m-4 mt-0 rounded-t-none border-t-0 mb-16">
                <CardContent className="p-4">
                  <div className="flex gap-2">
                    <Input
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      onKeyPress={handleKeyPress}
                      placeholder="Mesajınızı yazın..."
                      className="flex-1"
                    />
                    <Button onClick={sendMessage} size="icon" className="flex-shrink-0">
                      <Send className="h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Resizer Bar */}
            <div
              className="w-1 bg-gray-300 hover:bg-blue-500 cursor-ew-resize flex items-center justify-center"
              onMouseDown={handleMouseDown}
            >
              <GripHorizontal className="h-[50px] w-5 text-gray-800" />
            </div>

            {/* Right Panel - Scrollable */}
            <div className="flex-1 p-6">
              <div className="mb-4 space-y-6">
                {/* Additional Cards for Testing Scroll */}
                <HotelStats setHotelBooks={setHotelBooks} setHotelBooksLoading={setHotelBooksLoading} />
                <PerforamceMetrics hotelBooks={hotelBooksStg} hotelBooksStgLoading={hotelBooksStgLoading} />
                <TopPerformingHotels />
                <Card>
                  <CardHeader>
                    <CardTitle>Aylık Trendler</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-64">
                      Ciro
                      {monthlyTrendsLoading ? (
                        <div className="flex items-center justify-center h-full">
                          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
                        </div>
                      ) : (
                        <ResponsiveContainer width="100%" height="100%">
                          <BarChart data={(() => {
                            const chartData = [];
                            const orderedMonths = [
                              'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                              'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
                            ];

                            // Create a map for easier lookup by month
                            const dataMap = {};
                            monthlyTrendsData.forEach(item => {
                              const monthName = new Date(item.currentYear, item.month - 1).toLocaleString('en-US', { month: 'short' });
                              dataMap[monthName] = item;
                            });

                            orderedMonths.forEach(monthName => {
                              const item = dataMap[monthName] || {
                                month: monthName,
                                revenue_currentYear: 0,
                                reservations_currentYear: 0,
                                revenue_prevYear: 0,
                                reservations_prevYear: 0,
                                currentYear: currentYear,
                                prevYear: previousYear
                              };
                              chartData.push({
                                month: monthName,
                                [`${item.currentYear} Revenue`]: item.revenue_currentYear,
                                [`${item.prevYear} Revenue`]: item.revenue_prevYear,
                              });
                            });
                            return chartData;
                          })()}>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="month" />
                            <YAxis />
                            <Tooltip
                              formatter={(value, name) => [
                                new Intl.NumberFormat('tr-TR', { style: 'currency', currency: 'TRY', minimumFractionDigits: 0, maximumFractionDigits: 0 }).format(value),
                                name
                              ]}
                            />
                            <Bar dataKey={`${previousYear} Revenue`} fill="#94a3b8" name={`${previousYear} Revenue`} />
                            <Bar dataKey={`${currentYear} Revenue`} fill="#1e40af" name={`${currentYear} Revenue`} />
                          </BarChart>
                        </ResponsiveContainer>
                      )}
                    </div>

                    <div className="h-64">
                      <h4 className="text-sm font-medium text-gray-600 mt-8">Rezervasyonlar</h4>
                      {monthlyTrendsLoading ? (
                        <div className="flex items-center justify-center h-full">
                          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
                        </div>
                      ) : (
                        <ResponsiveContainer width="100%" height="100%">
                          <BarChart data={(() => {
                            const chartData = [];
                            const orderedMonths = [
                              'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                              'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
                            ];

                            // Create a map for easier lookup by month
                            const dataMap = {};
                            monthlyTrendsData.forEach(item => {
                              const monthName = new Date(item.currentYear, item.month - 1).toLocaleString('en-US', { month: 'short' });
                              dataMap[monthName] = item;
                            });

                            orderedMonths.forEach(monthName => {
                              const item = dataMap[monthName] || {
                                month: monthName,
                                revenue_currentYear: 0,
                                reservations_currentYear: 0,
                                revenue_prevYear: 0,
                                reservations_prevYear: 0,
                                currentYear: currentYear,
                                prevYear: previousYear
                              };
                              chartData.push({
                                month: monthName,
                                [`${item.currentYear} Revenue`]: item.reservations_currentYear,
                                [`${item.prevYear} Revenue`]: item.reservations_prevYear,
                              });
                            });
                            return chartData;
                          })()}>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="month" />
                            <YAxis />
                            <Tooltip
                              formatter={(value, name) => [
                                new Intl.NumberFormat('tr-TR', { minimumFractionDigits: 0, maximumFractionDigits: 0 }).format(value),
                                name
                              ]}
                            />
                            <Bar dataKey={`${previousYear} Revenue`} fill="#94a3b8" name={`${previousYear} Revenue`} />
                            <Bar dataKey={`${currentYear} Revenue`} fill="#1e40af" name={`${currentYear} Revenue`} />
                          </BarChart>
                        </ResponsiveContainer>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>
              {/* <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Total Users</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-4xl font-bold">1,234</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader>
                    <CardTitle>Active Sessions</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-4xl font-bold">567</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader>
                    <CardTitle>Revenue</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-4xl font-bold">$89,012</p>
                  </CardContent>
                </Card>
              </div> */}

              {/* <div className="mt-6 space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Recent Activity</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      <li>User &apos;John Doe&apos; logged in.</li>
                      <li>New report &apos;Sales Q1&apos; generated.</li>
                      <li>Settings updated by &apos;Admin&apos;.</li>
                      <li>User &apos;Jane Smith&apos; registered.</li>
                      <li>User &apos;John Doe&apos; logged in.</li>
                      <li>New report &apos;Sales Q1&apos; generated.</li>
                      <li>Settings updated by &apos;Admin&apos;.</li>
                      <li>User &apos;Jane Smith&apos; registered.</li>
                      <li>User &apos;John Doe&apos; logged in.</li>
                      <li>New report &apos;Sales Q1&apos; generated.</li>
                      <li>Settings updated by &apos;Admin&apos;.</li>
                      <li>User &apos;Jane Smith&apos; registered.</li>
                      <li>User &apos;John Doe&apos; logged in.</li>
                      <li>New report &apos;Sales Q1&apos; generated.</li>
                      <li>Settings updated by &apos;Admin&apos;.</li>
                      <li>User &apos;Jane Smith&apos; registered.</li>
                      <li>User &apos;John Doe&apos; logged in.</li>
                      <li>New report &apos;Sales Q1&apos; generated.</li>
                      <li>Settings updated by &apos;Admin&apos;.</li>
                      <li>User &apos;Jane Smith&apos; registered.</li>
                      <li>User &apos;John Doe&apos; logged in.</li>
                      <li>New report &apos;Sales Q1&apos; generated.</li>
                      <li>Settings updated by &apos;Admin&apos;.</li>
                      <li>User &apos;Jane Smith&apos; registered.</li>
                      <li>User &apos;John Doe&apos; logged in.</li>
                      <li>New report &apos;Sales Q1&apos; generated.</li>
                      <li>Settings updated by &apos;Admin&apos;.</li>
                      <li>User &apos;Jane Smith&apos; registered.</li>
                      <li>User &apos;John Doe&apos; logged in.</li>
                      <li>New report &apos;Sales Q1&apos; generated.</li>
                      <li>Settings updated by &apos;Admin&apos;.</li>
                      <li>User &apos;Jane Smith&apos; registered.</li>
                    </ul>
                  </CardContent>
                </Card>
              </div> */}
            </div>
          </main>

        </div>
    </Layout>
    </ProtectedRoute>
  );
};
export default AdminDashboard;

const HotelStats = props => {
  const { data: session } = useSession();

  const [hotelStats, setHotelStats] = useState(null);
  const [hotelBooks, setHotelBooks] = useState(null);
  const [loading, setLoading] = useState(true);
  const [loadingBooks, setLoadingBooks] = useState(true);

  useEffect(() => {
    // fetchHotelStats();
    const fx = async () => {
      const data = await fetchHotelStats();
      props.setHotelBooksLoading && props.setHotelBooksLoading(true);
      try {
        const dataBooks = await fetchHotelBooks();
        props.setHotelBooks && props.setHotelBooks(dataBooks.data)
        props.setHotelBooksLoading && props.setHotelBooksLoading(false);
      } catch (e) {
        console.log('error', e);
        props.setHotelBooksLoading && props.setHotelBooksLoading(false);
      }
    };
    fx();
  }, []);

  const fetchHotelStats = async () => {
    try {
      const response = await fetch('/api/hotels/stats?w=hotelstatistics');
      if (response.ok) {
        const data = await response.json();
        setHotelStats(data);
        // await new Promise(resolve => setTimeout(resolve, 1500));
        return (data)
      }
    } catch (error) {
      console.error('Error fetching hotel stats:', error);
    } finally {
      setLoading(false);
    }
  };


  const fetchHotelBooks = async () => {
    try {
      const response = await fetch('/api/hotels/stats?w=books');
      if (response.ok) {
        const data = await response.json();
        setHotelBooks(data?.data);
        // await new Promise(resolve => setTimeout(resolve, 1500));
        return (data)
      }
    } catch (error) {
      console.error('Error fetching hotel stats:', error);
    } finally {
      setLoadingBooks(false);
    }
  };

  // if (loading) return <div>Loading...</div>;

  // if (!hotelStats) return <div>No data</div>;
  // console.log(hotelStats);

  const { toplam_tesis, listelenen_tesisler, tesis_durumlari } = hotelStats || {};
  const totalHotels = toplam_tesis || 0;
  const listedHotels = listelenen_tesisler || 0;
  const unlistedHotels = totalHotels - listedHotels;
  const listedPercentage = (listedHotels / totalHotels) * 100;
  const unlistedPercentage = 100 - listedPercentage;
  const yenibasvuruAdedi = tesis_durumlari?.find(item => item.TESIS_ONAY === 'Yeni Başvuru')?.count || 0;
  const kapaliTesisAdedi = tesis_durumlari?.find(item => item.TESIS_ONAY === 'Onaylandı' && item.DURUM === 'Tesis Kapalı')?.count || 0;
  const onayBekleyen = tesis_durumlari?.find(item => item.TESIS_ONAY === 'Onay Bekliyor' && item.DURUM !== 'Çalışmıyoruz')?.count || 0;

  const hotelBooksArr = Array.isArray(hotelBooks) && hotelBooks?.filter(item => item.donemTipi === 'BuAy');
  const { toplamRezvGunSayisi, toplamCiro, SATIS_DONEM, distinctTesisSayisi } = hotelBooksArr && hotelBooksArr[0] || {};

  return (
    <Card>
      <CardHeader>
        <CardTitle>Yurt içi Tesis İstatistikleri </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 gap-4">
          {loading ? (
            <>
              <div className="flex flex-col items-center justify-center py-2 w-full col-span-2">
                {/* Modern renkli bar loader */}
                <div className="w-32 h-2 bg-gray-200 rounded-full overflow-hidden relative">
                  <div className="absolute left-0 top-0 h-full w-1/3 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 animate-loader-bar" />
                </div>
                <p className="text-sm text-gray-400 mt-3">Yükleniyor...</p>
              </div>
              <style jsx>{`
                @keyframes loader-bar {
                  0% { left: -33%; }
                  100% { left: 100%; }
                }
                .animate-loader-bar {
                  animation: loader-bar 1.2s cubic-bezier(0.4,0,0.2,1) infinite;
                }
              `}</style>
            </>
          ) : (
            <>
              <div className="flex flex-wrap gap-4 w-full">
                <div className="flex-1 text-center">
                  <p className="text-2xl font-bold text-blue-600">{listedHotels || 0}</p>
                  <p className="text-sm text-gray-500">Total Hotels</p>
                </div>
                <div className="flex-1 text-center">
                  <p className="text-2xl font-bold text-green-600">{kapaliTesisAdedi || 0}</p>
                  <p className="text-sm text-gray-500">Listelenen Kapalı</p>
                </div>
                <div className="flex-1 text-center">
                  <p className="text-2xl font-bold text-green-600">{onayBekleyen || 0}</p>
                  <p className="text-sm text-gray-500">Onay Bekleyen</p>
                </div>
                <div className="flex-1 text-center">
                  <p className="text-2xl font-bold text-green-600">{yenibasvuruAdedi || 0}</p>
                  <p className="text-sm text-gray-500">Yeni Basvuru</p>
                </div>
              </div>

              <div className="text-center">
                {loadingBooks ? (
                  <>
                    {/* Küçük animasyonlu loader */}
                    <div className="w-12 h-2 bg-gray-200 rounded-full overflow-hidden relative mx-auto">
                      <div className="absolute left-0 top-0 h-full w-1/3 bg-gradient-to-r from-green-400 via-blue-400 to-purple-400 animate-loader-bar-mini" />
                    </div>
                    <p className="text-xs text-gray-400 mt-2">Yükleniyor...</p>
                    <style jsx>{`
                      @keyframes loader-bar-mini {
                        0% { left: -33%; }
                        100% { left: 100%; }
                      }
                      .animate-loader-bar-mini {
                        animation: loader-bar-mini 1s cubic-bezier(0.4,0,0.2,1) infinite;
                      }
                    `}</style>
                  </>
                ) : (
                  <>
                    {(() => {
                      const occupancyRate = ((distinctTesisSayisi || 0) / listedHotels * 100);
                      const rateClass = occupancyRate > 10 ? "text-blue-600" : "text-red-600";
                      return (
                        <p className={`text-2xl font-bold ${rateClass}`}>
                          {distinctTesisSayisi || 0} {' | '} {occupancyRate.toFixed(1) || 0}%
                        </p>
                      );
                    })()}
                    <p className="text-sm text-gray-500">{SATIS_DONEM} Occupancy Rate</p>
                  </>
                )}
              </div>
            </>
          )}
        </div>
      </CardContent>
    </Card>
  )
};
const PerforamceMetrics = props => {
  const { hotelBooks, hotelbooksloading } = props;
  const [hotelBooksStg, setHotelBooksStg] = useState(null);
  const [hotelBooksStgLoading, setHotelBooksStgLoading] = useState(true);

  useEffect(() => {
    setHotelBooksStg(hotelBooks);
  }, [props.hotelBooks]);

  useEffect(() => {
    setHotelBooksStgLoading(props.hotelBooksStgLoading);
  }, [props.hotelBooksStgLoading]);

  const hotelBooksBuAy = Array.isArray(hotelBooksStg) && hotelBooksStg?.filter(item => item.donemTipi === 'BuAy');
  const hotelBooksGecenAy = Array.isArray(hotelBooksStg) && hotelBooksStg?.filter(item => item.donemTipi === 'GecenAy');
  const hotelBooksGecenYilAyniAy = Array.isArray(hotelBooksStg) && hotelBooksStg?.filter(item => item.donemTipi === "GecenYilAyniAy");
  const { toplamRezvGunSayisi: RezvGunSayisiBuAy, toplamRezvSayisi: RezvSayisiBuAy, toplamCiro: CiroBuAy, SATIS_DONEM: BuAy, distinctTesisSayisi: TesisSayisiBuAy } = hotelBooksBuAy && hotelBooksBuAy[0] || {};
  const { toplamRezvGunSayisi: RezvGunSayisiGecenAy, toplamRezvSayisi: RezvSayisiGecenAy, toplamCiro: CiroGecenAy, SATIS_DONEM: GecenAy, distinctTesisSayisi: TesisSayisiGecenAy } = hotelBooksGecenAy && hotelBooksGecenAy[0] || {};
  const { toplamRezvGunSayisi: RezvGunSayisiGecenYil, toplamRezvSayisi: RezvSayisiGecenYil, toplamCiro: CiroGecenYil, SATIS_DONEM: GecenYil, distinctTesisSayisi: TesisSayisiGecenYil } = hotelBooksGecenYilAyniAy && hotelBooksGecenYilAyniAy[0] || {};

  let GunRatio = 0;
  if (BuAy) {
    const [year, month] = BuAy.split('-').map(Number);
    const now = new Date();
    // Eğer BuAy bu ay ise, bugünkü güne kadar olan oranı hesapla
    if (now.getFullYear() === year && (now.getMonth() + 1) === month) {
      const currentDay = now.getDate();
      const totalDays = new Date(year, month, 0).getDate();
      GunRatio = (currentDay / totalDays) * 100;
    } else {
      // Geçmiş veya gelecek ay ise, %100 olarak ata
      GunRatio = 100;
    }
  }
  // Format currency as TL, round to nearest whole number
  const formatTL = (value) => {
    if (typeof value !== 'number') return '-';
    // Sadece sayıyı göster, para birimi simgesi olmadan
    return value.toLocaleString('tr-TR', { maximumFractionDigits: 0 });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Performans Göstergeleri</CardTitle>
      </CardHeader>
      <CardContent>
        {hotelBooksStgLoading ? (
          <div className="flex flex-col items-center justify-center py-8">
            {/* Animasyonlu loader */}
            <div className="w-24 h-3 bg-gray-200 rounded-full overflow-hidden relative">
              <div className="absolute left-0 top-0 h-full w-1/3 bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 animate-loader-bar-metrics" />
            </div>
            <p className="text-sm text-gray-400 mt-4">Yükleniyor...</p>
            <style jsx>{`
              @keyframes loader-bar-metrics {
                0% { left: -33%; }
                100% { left: 100%; }
              }
              .animate-loader-bar-metrics {
                animation: loader-bar-metrics 1.2s cubic-bezier(0.4,0,0.2,1) infinite;
              }
            `}</style>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="flex justify-between">
              <span>Gelir (TL) &nbsp;
                <span className="text-xs text-gray-400">{GunRatio.toFixed(0)}% Ay Tamamlanma Oranı</span>
              </span>
              <span className="font-semibold text-green-600">{formatTL(CiroBuAy)}</span>
            </div>
            {/* Geçen Ay/Yıl Gelir Kıyaslaması */}

            <div className="flex justify-between mt-1">
              <span className="text-xs text-gray-400 ml-2">
                Geçen Ay | {GecenAy}:
              </span>
              <span className="text-xs text-gray-400">
                {CiroGecenAy && CiroBuAy
                  ? `${formatTL(CiroGecenAy)},   ${(((CiroBuAy) / CiroGecenAy) * 100).toFixed(1)}%`
                  : ''}
              </span>
            </div>

            <div className="flex justify-between mt-1">
              <span className="text-xs text-gray-400 ml-2">
                Geçen Yıl | {GecenYil}:
              </span>
              <span className="text-xs text-gray-400">
                {CiroGecenYil && CiroBuAy
                  ? `${formatTL(CiroGecenYil)},   ${(((CiroBuAy) / CiroGecenYil) * 100).toFixed(1)}%`
                  : ''}
              </span>
            </div>

            <div className="flex justify-between">
              <span>Rezervasyon</span>
              <span className="font-semibold text-green-600">{formatTL(RezvSayisiBuAy)}</span>
            </div>
            <div className="flex justify-between mt-1">
              <span className="text-xs text-gray-400 ml-2">
                Geçen Ay | {GecenAy}:
              </span>
              <span className="text-xs text-gray-400">
                {RezvSayisiGecenAy && RezvSayisiBuAy
                  ? `${formatTL(RezvSayisiGecenAy)},   ${(((RezvSayisiBuAy) / RezvSayisiGecenAy) * 100).toFixed(1)}%`
                  : ''}
              </span>
            </div>
            <div className="flex justify-between mt-1">
              <span className="text-xs text-gray-400 ml-2">
                Geçen Yıl | {GecenYil}:
              </span>
              <span className="text-xs text-gray-400">
                {RezvSayisiGecenYil && RezvSayisiBuAy
                  ? `${formatTL(RezvSayisiGecenYil)},   ${(((RezvSayisiBuAy) / RezvSayisiGecenYil) * 100).toFixed(1)}%`
                  : ''}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Tesis Sayısı &nbsp; </span>
              <span className="font-semibold text-green-600">{formatTL(TesisSayisiBuAy)}</span>
            </div>
            <div className="flex justify-between mt-1">
              <span className="text-xs text-gray-400 ml-2">
                Geçen Ay | {GecenAy}:
              </span>
              <span className="text-xs text-gray-400">
                {TesisSayisiGecenAy && TesisSayisiBuAy
                  ? `${formatTL(TesisSayisiGecenAy)},   ${(((TesisSayisiBuAy) / TesisSayisiGecenAy) * 100).toFixed(1)}%`
                  : ''}
              </span>
            </div>
            <div className="flex justify-between mt-1">
              <span className="text-xs text-gray-400 ml-2">
                Geçen Yıl | {GecenYil}:
              </span>
              <span className="text-xs text-gray-400">
                {TesisSayisiGecenYil && TesisSayisiBuAy
                  ? `${formatTL(TesisSayisiGecenYil)},   ${(((TesisSayisiBuAy) / TesisSayisiGecenYil) * 100).toFixed(1)}%`
                  : ''}
              </span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
const TopPerformingHotels = () => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>En İyi Performans Gösteren Tesisler</CardTitle>
      </CardHeader>
      <CardContent>
        <TopPerformingHotelsTabz />
      </CardContent>
    </Card>
  );
};
