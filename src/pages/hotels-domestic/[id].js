import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import ProtectedRoute from '@/components/ProtectedRoute';
import Layout from '@/components/layouts/Layout';
import {
  MapPin,
  Star,
  Phone,
  Globe,
  TrendingUp,
  TrendingDown,
  Users,
  Eye,
  DollarSign,
  Calendar,
  Building,
  ArrowLeft,
  ExternalLink
} from 'lucide-react';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell
} from 'recharts';
import dynamic from "next/dynamic";
import "leaflet/dist/leaflet.css";

// Dinamik import (SSR kapalı) ile React Leaflet Map
const MapContainer = dynamic(
  () => import("react-leaflet").then(mod => mod.MapContainer),
  { ssr: false }
);
const TileLayer = dynamic(
  () => import("react-leaflet").then(mod => mod.TileLayer),
  { ssr: false }
);
const Marker = dynamic(
  () => import("react-leaflet").then(mod => mod.Marker),
  { ssr: false }
);
const Popup = dynamic(
  () => import("react-leaflet").then(mod => mod.Popup),
  { ssr: false }
);

// Marker icon'u sadece client'ta oluştur
function useLeafletMarkerIcon() {
  const [icon, setIcon] = useState(null);
  useEffect(() => {
    if (typeof window !== "undefined") {
      // Dinamik import ile leaflet sadece client'ta yüklenir
      import("leaflet").then(L => {
        setIcon(
          new L.Icon({
            iconUrl: "https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon.png",
            iconRetinaUrl: "https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon-2x.png",
            shadowUrl: "https://unpkg.com/leaflet@1.9.4/dist/images/marker-shadow.png",
            iconSize: [25, 41],
            iconAnchor: [12, 41],
            popupAnchor: [1, -34],
            shadowSize: [41, 41],
          })
        );
      });
    }
  }, []);
  return icon;
}

function useNearbyLeafletMarkerIcon() {
  const [icon, setIcon] = useState(null);
  useEffect(() => {
    if (typeof window !== "undefined") {
      import("leaflet").then(L => {
        setIcon(
          new L.Icon({
            iconUrl: "https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-grey.png",
            iconRetinaUrl: "https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-grey.png",
            shadowUrl: "https://unpkg.com/leaflet@1.9.4/dist/images/marker-shadow.png",
            iconSize: [18, 30], // Daha küçük
            iconAnchor: [9, 30],
            popupAnchor: [1, -24],
            shadowSize: [30, 30],
            className: "nearby-marker-icon"
          })
        );
      });
    }
  }, []);
  return icon;
}

const HotelDetail = () => {
  const router = useRouter();
  const { id } = router.query;
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [hotelData, setHotelData] = useState(null);
  const [salesData, setSalesData] = useState([]);
  const [trafficData, setTrafficData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [nearbyHotels, setNearbyHotels] = useState([]);
  const [salesAnalytics, setSalesAnalytics] = useState({
    currentYear: [],
    previousYear: [],
    currentYearToDate: [],
    salesChannelTrend: [],
    roomTypeTrend: [],
    bayiDistribution: [],
    currentMonthBayiDistribution: []
  });
  const markerIcon = useLeafletMarkerIcon();
  const nearbyMarkerIcon = useNearbyLeafletMarkerIcon();

  const toggleDrawer = () => setIsDrawerOpen(!isDrawerOpen);

  useEffect(() => {
    if (id) {
      fetchHotelData();
    }
  }, [id]);

  // Fetch nearby hotels after hotelData is loaded
  useEffect(() => {
    if (hotelData?.LATITUDE && hotelData?.LONGITUDE) {
      fetchNearbyHotels();
    }
    // eslint-disable-next-line
  }, [hotelData?.LATITUDE, hotelData?.LONGITUDE]);

  const fetchHotelData = async () => {
    try {
      setLoading(true);

      // Fetch hotel basic info
      const hotelResponse = await fetch(`/api/hotels/${id}`);
      if (!hotelResponse.ok) throw new Error('Hotel not found');
      const hotelResult = await hotelResponse.json();
      setHotelData(hotelResult.data);

      // Fetch sales data
      const salesResponse = await fetch(`/api/hotels/${id}/sales?limit=12`);
      if (salesResponse.ok) {
        const salesResult = await salesResponse.json();
        setSalesData(salesResult.data || []);
      }

      // Fetch detailed sales analytics
      const analyticsResponse = await fetch(`/api/hotels/${id}/sales-analytics`);
      if (analyticsResponse.ok) {
        const analyticsResult = await analyticsResponse.json();
        setSalesAnalytics(analyticsResult.data || {
          currentYear: [],
          previousYear: [],
          currentYearToDate: [],
          salesChannelTrend: [],
          roomTypeTrend: [],
          bayiDistribution: [],
          currentMonthBayiDistribution: []
        });
      }

      // Fetch traffic data
      const trafficResponse = await fetch(`/api/hotels/${id}/traffic?limit=12`);
      if (trafficResponse.ok) {
        const trafficResult = await trafficResponse.json();
        setTrafficData(trafficResult.data || []);
      }

    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const fetchNearbyHotels = async () => {
    try {
      // örnek: /api/hotels/nearby?lat=...&lng=...&radius=1
      const res = await fetch(
        `/api/hotels/nearby?lat=${hotelData.LATITUDE}&lng=${hotelData.LONGITUDE}&radius=1&id=${id}`
      );
      if (res.ok) {
        const result = await res.json();
        setNearbyHotels(result.data || []);
      }
    } catch (e) {
      // Hata yönetimi opsiyonel
    }
  };

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const formatNumber = (value) => {
    return new Intl.NumberFormat('tr-TR').format(value);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'Onaylandı': return 'bg-green-100 text-green-800';
      case 'Reddedildi': return 'bg-red-100 text-red-800';
      case 'Beklemede': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTrendIcon = (growth) => {
    if (growth > 0) return <TrendingUp className="h-4 w-4 text-green-500" />;
    if (growth < 0) return <TrendingDown className="h-4 w-4 text-red-500" />;
    return <div className="h-4 w-4" />;
  };

  if (loading) {
    return (
      <ProtectedRoute>
        <Layout isDrawerOpen={isDrawerOpen} toggleDrawer={toggleDrawer} title="Hotel Detail">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
          </div>
        </Layout>
      </ProtectedRoute>
    );
  }

  if (error) {
    return (
      <ProtectedRoute>
        <Layout isDrawerOpen={isDrawerOpen} toggleDrawer={toggleDrawer} title="Hotel Detail">
          <div className="text-center py-12">
            <div className="text-red-500 text-xl mb-4">Error: {error}</div>
            <Button onClick={() => router.back()}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Go Back
            </Button>
          </div>
        </Layout>
      </ProtectedRoute>
    );
  }

  if (!hotelData) {
    return (
      <ProtectedRoute>
        <Layout isDrawerOpen={isDrawerOpen} toggleDrawer={toggleDrawer} title="Hotel Detail">
          <div className="text-center py-12">
            <div className="text-gray-500 text-xl mb-4">Hotel not found</div>
            <Button onClick={() => router.back()}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Go Back
            </Button>
          </div>
        </Layout>
      </ProtectedRoute>
    );
  }

  // Bugünün ve yarının tarihini "DD.MM.YYYY" formatında al
  const today = new Date();
  const tomorrow = new Date(today);
  tomorrow.setDate(today.getDate() + 1);

  const pad = (n) => n.toString().padStart(2, '0');
  const formatDate = (d) =>
    `${pad(d.getDate())}.${pad(d.getMonth() + 1)}.${d.getFullYear()}`;

  const dateParam = `tarih:${formatDate(today)},${formatDate(tomorrow)}`;

  return (
    <ProtectedRoute>
      <Layout
        isDrawerOpen={isDrawerOpen}
        toggleDrawer={toggleDrawer}
        title={hotelData.TESIS_ADI}
        breadCrumbs={['Home', 'Hotels', hotelData.TESIS_ADI]}
      >
        <div className="space-y-6 max-w-7xl mx-auto">
          {/* Header Section */}
          <div className="flex items-center justify-between">
            <Button variant="outline" onClick={() => router.back()}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <div className="flex items-center space-x-2">
              <Badge className={getStatusColor(hotelData.TESIS_ONAY)}>
                {hotelData.TESIS_ONAY} - {hotelData.DURUM}
              </Badge>
              {hotelData.WEBSITE_LISTED === 'TRUE' && (
                <Badge variant="outline" className="bg-blue-50 text-blue-700">
                  <Globe className="h-3 w-3 mr-1" />
                  Listed
                </Badge>
              )}
            </div>
          </div>

          {/* Hotel Basic Info */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Building className="h-5 w-5" />
                <span>{hotelData.TESIS_ADI}</span>
                {hotelData.SEO_URL && (
                  <Badge variant="outline" className="text-xs">
                    <a
                      href={`https://www.tatilsepeti.com/${hotelData.SEO_URL}?ara=oda:2;${dateParam};click:true`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center text-blue-600 hover:underline"
                    >
                      Tatilsepeti'nde Görüntüle
                      <ExternalLink className="h-3 w-3 ml-1" />
                    </a>
                  </Badge>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div  className="grid grid-cols-1 md:grid-cols-2 gap-6">


                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <div>
                    {/* <h3 className="font-semibold text-lg mb-2">{hotelData.TESIS_ADI}</h3> */}
                    <div className="space-y-2 text-sm text-gray-600">
                      <div className="flex items-center space-x-2">
                        <Star className="h-4 w-4" />
                        <span>{hotelData.KATEGORI}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <MapPin className="h-4 w-4" />
                        <span>{hotelData.BOLGE_ADI}, {hotelData.ALT_BOLGE_ADI}</span> {hotelData.BOLGE_DETAY && (
                          <div className="text-gray-500">{hotelData.BOLGE_DETAY}</div>
                        )}
                      </div>

                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium mb-2">Contact Information</h4>
                    <div className="space-y-2 text-sm text-gray-600">
                      {hotelData.TEL && (
                        <div className="flex items-center space-x-2">
                          <Phone className="h-4 w-4" />
                          <span>{hotelData.TEL}</span>
                        </div>
                      )}
                      {hotelData.ADRES1_DETAY && (
                        <div className="text-gray-500">{hotelData.ADRES1_DETAY}</div>
                      )}
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium mb-2">Facility Details</h4>
                    <div className="space-y-2 text-sm text-gray-600">
                      {hotelData.TESIS_KAPASITE && (
                        <div>Capacity: {hotelData.TESIS_KAPASITE} rooms</div>
                      )}
                      {hotelData.TESIS_PUANI > 0 && (
                        <div>Rating: {hotelData.TESIS_PUANI}/10</div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Harita ve Tatilsepeti Linki */}
                <div className="flex ">
                  {hotelData.LATITUDE && hotelData.LONGITUDE && markerIcon && (
                    <div className="w-full h-64 rounded-lg overflow-hidden border">
                      <MapContainer
                        center={[Number(hotelData.LATITUDE), Number(hotelData.LONGITUDE)]}
                        zoom={15}
                        scrollWheelZoom={false}
                        style={{ height: "100%", width: "100%" }}
                      >
                        <TileLayer
                          attribution='&copy; <a href="https://osm.org/copyright">OpenStreetMap</a>'
                          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                        />
                        {/* Main hotel marker */}
                        <Marker
                          position={[Number(hotelData.LATITUDE), Number(hotelData.LONGITUDE)]}
                          icon={markerIcon}
                        >
                          <Popup>
                            {hotelData.TESIS_ADI}
                          </Popup>
                        </Marker>
                        {/* Nearby hotels */}
                        {nearbyHotels.map((nhotel) => (
                          <Marker
                            key={nhotel._id}
                            position={[Number(nhotel.LATITUDE), Number(nhotel.LONGITUDE)]}
                            icon={nearbyMarkerIcon}
                          >
                            <Popup>
                              <div className="flex flex-col">
                                <span className="font-semibold">{nhotel.TESIS_ADI}</span>
                                <div className="flex items-center space-x-2 mt-1">
                                  <Badge variant="outline">
                                    <a
                                      href={`https://www.tatilsepeti.com/${nhotel.SEO_URL}?ara=oda:2;${dateParam};click:true`}
                                      target="_blank"
                                      rel="noopener noreferrer"
                                      className="text-blue-600 hover:underline"
                                    >
                                      TSP
                                    </a>
                                  </Badge>
                                  <Badge variant="outline">
                                    <a
                                      href={`/hotels-domestic/${nhotel.TESIS_ID}`}
                                      rel="noopener noreferrer"
                                      className="text-blue-600 hover:underline"
                                    >
                                      Tesis Git
                                    </a>
                                  </Badge>
                                </div>
                              </div>
                            </Popup>
                          </Marker>
                        ))}
                      </MapContainer>
                    </div>
                  )}
                </div>

              </div>


              {/* {hotelData.KONAKLAMA_ACIKLAMA && (
                <div className="mt-6">
                  <h4 className="font-medium mb-2">Accommodation Details</h4>
                  <div
                    className="text-sm text-gray-600 prose max-w-none"
                    dangerouslySetInnerHTML={{ __html: hotelData.KONAKLAMA_ACIKLAMA }}
                  />
                </div>
              )} */}
            </CardContent>
          </Card>

          {/* Analytics Tabs */}
          <Tabs defaultValue="sales" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="sales">Sales Analytics</TabsTrigger>
              <TabsTrigger value="traffic">Web Traffic</TabsTrigger>
            </TabsList>

            <TabsContent value="sales" className="space-y-6">
              {/* 2025 vs Current Month Summary Cards */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">2025 Total Ciro</p>
                        <p className="text-2xl font-bold">
                          {formatCurrency(salesAnalytics.currentYear.reduce((sum, item) => sum + item.totalRevenue, 0))}
                        </p>
                      </div>
                      <DollarSign className="h-8 w-8 text-green-500" />
                    </div>

                    <div className="flex items-center justify-between mt-4">
                      <div>
                        <p className="text-sm font-medium text-gray-600">2025 MTD Ciro</p>
                        <p className="text-2xl font-bold">
                          {formatCurrency(salesAnalytics.currentYear.find(item => item._id === `${new Date().getFullYear()}-${(new Date().getMonth() + 1).toString().padStart(2, '0')}`)?.totalRevenue || 0)}
                        </p>
                      </div> 
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">2025 Total Reservations</p>
                        <p className="text-2xl font-bold">
                          {formatNumber(salesAnalytics.currentYear.reduce((sum, item) => sum + item.totalReservations, 0))}
                        </p>
                      </div>
                      <Calendar className="h-8 w-8 text-blue-500" />
                    </div>
                    <div className="flex items-center justify-between mt-4">
                      <div>
                        <p className="text-sm font-medium text-gray-600">2025 MTD Reservations</p>
                        <p className="text-2xl font-bold">
                          {formatNumber(salesAnalytics.currentYear.find(item => item._id === `${new Date().getFullYear()}-${(new Date().getMonth() + 1).toString().padStart(2, '0')}`)?.totalReservations || 0)}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">2025 Total Guests</p>
                        <p className="text-2xl font-bold">
                          {formatNumber(salesAnalytics.currentYear.reduce((sum, item) => sum + item.totalGuests, 0))}
                        </p>
                      </div>
                      <Users className="h-8 w-8 text-purple-500" />
                    </div>
                    <div className="flex items-center justify-between mt-4">
                      <div>
                        <p className="text-sm font-medium text-gray-600">2025 MTD Guests</p>
                        <p className="text-2xl font-bold">
                          {formatNumber(salesAnalytics.currentYear.find(item => item._id === `${new Date().getFullYear()}-${(new Date().getMonth() + 1).toString().padStart(2, '0')}`)?.totalGuests || 0)}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>


                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Avg Ciro/Rezv (Bu Yıl)</p>
                        <p className="text-2xl font-bold">
                          {formatCurrency(
                            (salesAnalytics.currentYear.reduce((sum, item) => sum + item.totalRevenue, 0) /
                            salesAnalytics.currentYear.reduce((sum, item) => sum + item.totalReservations, 0)) || 0
                          )}
                        </p>
                      </div>
                      <TrendingUp className="h-8 w-8 text-orange-500" />
                    </div>
                    <div className="flex items-center justify-between mt-4">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Avg Ciro/Rezv (Bu Ay)</p>
                        <p className="text-2xl font-bold">
                          {formatCurrency(
                            (salesAnalytics.currentYear.find(item => item._id === `${new Date().getFullYear()}-${(new Date().getMonth() + 1).toString().padStart(2, '0')}`)?.totalRevenue || 0) /
                            (salesAnalytics.currentYear.find(item => item._id === `${new Date().getFullYear()}-${(new Date().getMonth() + 1).toString().padStart(2, '0')}`)?.totalReservations || 0) || 0
                          )}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

              </div>

              {/* Sales Trend Chart - Current vs Previous Year */}
              <Card>
                <CardHeader>
                  <CardTitle>Satis Trendi</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-80">
                    {typeof window !== 'undefined' && (
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart data={(() => {
                          // Combine current and previous year data for comparison
                          const combinedData = [];
                          const currentYear = new Date().getFullYear();
                          const previousYear = currentYear - 1;

                          // Create a map for easier lookup
                          const currentYearMap = {};
                          salesAnalytics.currentYear.forEach(item => {
                            const month = item._id.split('-')[1];
                            currentYearMap[month] = item;
                          });

                          const previousYearMap = {};
                          salesAnalytics.previousYear.forEach(item => {
                            const month = item._id.split('-')[1];
                            previousYearMap[month] = item;
                          });

                          // Generate data for all months
                          for (let month = 1; month <= 12; month++) {
                            const monthStr = month.toString().padStart(2, '0');
                            const currentData = currentYearMap[monthStr] || { totalRevenue: 0, totalReservations: 0 };
                            const previousData = previousYearMap[monthStr] || { totalRevenue: 0, totalReservations: 0 };

                            combinedData.push({
                              month: monthStr,
                              [`${currentYear} Revenue`]: currentData.totalRevenue,
                              [`${previousYear} Revenue`]: previousData.totalRevenue,
                              [`${currentYear} Reservations`]: currentData.totalReservations,
                              [`${previousYear} Reservations`]: previousData.totalReservations
                            });
                          }

                          return combinedData;
                        })()}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="month" />
                          <YAxis />
                          <Tooltip
                            formatter={(value, name) => [
                              name.includes('Revenue') ? formatCurrency(value) : formatNumber(value),
                              name
                            ]}
                          />
                          <Bar dataKey={`${new Date().getFullYear() - 1} Revenue`} fill="#94a3b8" name={`${new Date().getFullYear() - 1} Revenue`} />
                          <Bar dataKey={`${new Date().getFullYear()} Revenue`} fill="#1e40af" name={`${new Date().getFullYear()} Revenue`} />
                        </BarChart>
                      </ResponsiveContainer>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Sales Channel and Room Type Analysis */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Sales Channel Trend */}
                <Card>
                  <CardHeader>
                    <CardTitle>Satış Kanalı Gelişimi</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-64">
                      {typeof window !== 'undefined' && salesAnalytics.salesChannelTrend.length > 0 && (
                        <ResponsiveContainer width="100%" height="100%">
                          <BarChart data={salesAnalytics.salesChannelTrend.map(item => {
                            const channelData = { period: item._id };
                            item.channels.forEach(channel => {
                              channelData[channel.channel] = channel.revenue;
                            });
                            return channelData;
                          })}>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="period" />
                            <YAxis />
                            <Tooltip formatter={(value) => formatCurrency(value)} />
                            {(() => {
                              const allChannels = new Set();
                              salesAnalytics.salesChannelTrend.forEach(item => {
                                item.channels.forEach(channel => {
                                  allChannels.add(channel.channel);
                                });
                              });
                              const COLORS = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4', '#f97316', '#84cc16']; // More colors for variety
                              return Array.from(allChannels).map((channelName, index) => (
                                <Bar key={channelName} dataKey={channelName} stackId="a" fill={COLORS[index % COLORS.length]} />
                              ));
                            })()}
                          </BarChart>
                        </ResponsiveContainer>
                      )}
                    </div>
                  </CardContent>
                </Card>

                {/* Room Type Trend */}
                <Card>
                  <CardHeader>
                    <CardTitle>Populer Oda Tipleri</CardTitle>
                  </CardHeader> 
                    
                  <CardContent>
                    <div className="h-64">
                      {typeof window !== 'undefined' && salesAnalytics.roomTypeTrend.length > 0 && (
                        <ResponsiveContainer width="100%" height="100%">
                          <PieChart>
                            <Pie
                              data={(() => {
                                const currentYear = new Date().getFullYear().toString();
                                const currentYearData = salesAnalytics.roomTypeTrend.filter(item =>
                                  item._id.startsWith(currentYear)
                                );

                                const roomTypeTotals = {};
                                currentYearData.forEach(monthData => {
                                  monthData.roomTypes.forEach(room => {
                                    roomTypeTotals[room.roomType] = (roomTypeTotals[room.roomType] || 0) + room.reservations;
                                  });
                                });

                                return Object.keys(roomTypeTotals).map(roomType => ({
                                  name: roomType,
                                  value: roomTypeTotals[roomType],
                                }));
                              })()}
                              cx="50%"
                              cy="50%"
                              labelLine={false}
                              label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                              outerRadius={80}
                              fill="#8884d8"
                              dataKey="value"
                            >
                              {(() => {
                                const currentYear = new Date().getFullYear().toString();
                                const currentYearData = salesAnalytics.roomTypeTrend.filter(item =>
                                  item._id.startsWith(currentYear)
                                );

                                const roomTypeTotals = {};
                                currentYearData.forEach(monthData => {
                                  monthData.roomTypes.forEach(room => {
                                    roomTypeTotals[room.roomType] = (roomTypeTotals[room.roomType] || 0) + room.reservations;
                                  });
                                });

                                const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#A28DFF', '#FF6F61', '#6A0572', '#AB47BC']; // More colors for variety
                                return Object.keys(roomTypeTotals).map((entry, index) => (
                                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                                ));
                              })()}
                            </Pie>
                            <Tooltip formatter={(value) => formatNumber(value)} />
                          </PieChart>
                        </ResponsiveContainer>
                      )}
                    </div>

                  </CardContent>
                </Card>
              </div>

              {/* Bayi Distribution Analysis */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* 2025 Bayi Distribution */}
                <Card>
                  <CardHeader>
                    <CardTitle>2025 Bayi Dağılımı</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-64 mb-4">
                      {typeof window !== 'undefined' && salesAnalytics.bayiDistribution.length > 0 && (
                        <ResponsiveContainer width="100%" height="100%">
                          <PieChart>
                            <Pie
                              data={salesAnalytics.bayiDistribution.map((item, index) => ({
                                name: item._id,
                                value: item.totalRevenue,
                                fill: `hsl(${index * 45}, 70%, 50%)`
                              }))}
                              cx="50%"
                              cy="50%"
                              labelLine={false}
                              label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                              outerRadius={80}
                              fill="#8884d8"
                              dataKey="value"
                            />
                            <Tooltip formatter={(value) => formatCurrency(value)} />
                          </PieChart>
                        </ResponsiveContainer>
                      )}
                    </div>

                    {/* Bayi Distribution Table */}
                    {salesAnalytics.bayiDistribution.length > 0 && (
                      <div className="mt-4">
                        <h4 className="font-semibold text-md mb-2">Detaylar</h4>
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Bayi</TableHead>
                              <TableHead className="text-right">Revenue</TableHead>
                              <TableHead className="text-right">Reservations</TableHead>
                              <TableHead className="text-right">Revenue Share (%)</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {salesAnalytics.bayiDistribution.map((item, index) => {
                              const totalRevenue = salesAnalytics.bayiDistribution.reduce((sum, b) => sum + b.totalRevenue, 0);
                              const revenueShare = totalRevenue > 0 ? (item.totalRevenue / totalRevenue) * 100 : 0;
                              return (
                                <TableRow key={index}>
                                  <TableCell className="font-medium">{item._id}</TableCell>
                                  <TableCell className="text-right">{formatCurrency(item.totalRevenue)}</TableCell>
                                  <TableCell className="text-right">{formatNumber(item.totalReservations)}</TableCell>
                                  <TableCell className="text-right">{revenueShare.toFixed(1)}%</TableCell>
                                </TableRow>
                              );
                            })}
                          </TableBody>
                        </Table>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Current Month Bayi Distribution */}
                <Card>
                  <CardHeader>
                    <CardTitle>Bu ay için bayi dağılımı</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-64">
                      {typeof window !== 'undefined' && salesAnalytics.currentMonthBayiDistribution.length > 0 && (
                        <ResponsiveContainer width="100%" height="100%">
                          <PieChart>
                            <Pie
                              data={salesAnalytics.currentMonthBayiDistribution.map((item, index) => ({
                                name: item._id,
                                value: item.totalRevenue,
                                fill: `hsl(${index * 45 + 180}, 70%, 50%)`
                              }))}
                              cx="50%"
                              cy="50%"
                              labelLine={false}
                              label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                              outerRadius={80}
                              fill="#8884d8"
                              dataKey="value"
                            />
                            <Tooltip formatter={(value) => formatCurrency(value)} />
                          </PieChart>
                        </ResponsiveContainer>
                      )}
                    </div>

                    {/* Current Month Bayi Distribution Table */}
                    {salesAnalytics.currentMonthBayiDistribution.length > 0 && (
                      <div className="mt-4">
                        <h4 className="font-semibold text-md mb-2">Detaylar</h4>
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Bayi</TableHead>
                              <TableHead className="text-right">Revenue</TableHead>
                              <TableHead className="text-right">Reservations</TableHead>
                              <TableHead className="text-right">Revenue Share (%)</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {salesAnalytics.currentMonthBayiDistribution.map((item, index) => {
                              const totalRevenue = salesAnalytics.currentMonthBayiDistribution.reduce((sum, b) => sum + b.totalRevenue, 0);
                              const revenueShare = totalRevenue > 0 ? (item.totalRevenue / totalRevenue) * 100 : 0;
                              return (
                                <TableRow key={index}>
                                  <TableCell className="font-medium">{item._id}</TableCell>
                                  <TableCell className="text-right">{formatCurrency(item.totalRevenue)}</TableCell>
                                  <TableCell className="text-right">{formatNumber(item.totalReservations)}</TableCell>
                                  <TableCell className="text-right">{revenueShare.toFixed(1)}%</TableCell>
                                </TableRow>
                              );
                            })}
                          </TableBody>
                        </Table>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="traffic" className="space-y-6">
              {/* Traffic Summary Cards */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {trafficData.length > 0 && (
                  <>
                    <Card>
                      <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium text-gray-600">Total Users</p>
                            <p className="text-2xl font-bold">
                              {formatNumber(trafficData.reduce((sum, item) => sum + item.totalUsers, 0))}
                            </p>
                          </div>
                          <Users className="h-8 w-8 text-blue-500" />
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium text-gray-600">Total Views</p>
                            <p className="text-2xl font-bold">
                              {formatNumber(trafficData.reduce((sum, item) => sum + item.totalItemsViewed, 0))}
                            </p>
                          </div>
                          <Eye className="h-8 w-8 text-green-500" />
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium text-gray-600">Web Revenue</p>
                            <p className="text-2xl font-bold">
                              {formatCurrency(trafficData.reduce((sum, item) => sum + item.totalRevenue, 0))}
                            </p>
                          </div>
                          <DollarSign className="h-8 w-8 text-purple-500" />
                        </div>
                      </CardContent>
                    </Card>
                  </>
                )}
              </div>

              {/* Traffic Trend Chart */}
              {trafficData.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle>Web Traffic Trends</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-80">
                      {typeof window !== 'undefined' && (
                        <ResponsiveContainer width="100%" height="100%">
                          <BarChart data={trafficData}>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="_id" />
                            <YAxis />
                            <Tooltip
                              formatter={(value, name) => [
                                formatNumber(value),
                                name === 'totalUsers' ? 'Users' : 'Views'
                              ]}
                            />
                            <Bar dataKey="totalUsers" fill="#3b82f6" name="Users" />
                            <Bar dataKey="totalItemsViewed" fill="#10b981" name="Views" />
                          </BarChart>
                        </ResponsiveContainer>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>
          </Tabs>
        </div>
      </Layout>
    </ProtectedRoute>
  );
};

export default HotelDetail;

<style jsx global>{`
  .leaflet-marker-icon.nearby-marker-icon {
    filter: brightness(1.7) saturate(0.7);
  }
`}</style>
