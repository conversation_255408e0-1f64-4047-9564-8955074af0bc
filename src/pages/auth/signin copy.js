import { useState, useEffect } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { ArrowLeft } from 'lucide-react';
import { LoginForm } from '@/components/auth/LoginForm';
import { useAuth } from '@/lib/contexts/AuthContext';
import { LanguageSwitcher } from '@/components/ui/LanguageSwitcher';
import { en } from '@/translations/en';
import { tr } from '@/translations/tr';
import { jwtDecode as jwt_decode } from 'jwt-decode';

export default function SignIn() {
  const router = useRouter();
  const { locale } = router;
  const t = locale === 'tr' ? tr : en;
  const { isAuthenticated, status, user } = useAuth();
  const { registered } = router.query;
  const [backgroundImage, setBackgroundImage] = useState('');
  const [isDarkMode, setIsDarkMode] = useState(false);

  useEffect(() => {
    if (status !== 'loading' && isAuthenticated && user) {
      // Token kontrolü
      if (user.token) {
        try {
          const decodedToken = jwt_decode(user.token);
          const tokenExp = new Date(decodedToken.exp * 1000);
          // console.log('Access Token expires at:', tokenExp.toLocaleString());
          // console.log('Is Access Token expired:', tokenExp < new Date());
        } catch (error) {
          console.error('Error decoding access token:', error);
        }
      }

      // Refresh Token kontrolü
      if (user.refreshToken) {
        try {
          const decodedRefreshToken = jwt_decode(user.refreshToken);
          const refreshTokenExp = new Date(decodedRefreshToken.exp * 1000);
          // console.log('Refresh Token expires at:', refreshTokenExp.toLocaleString());
          // console.log('Is Refresh Token expired:', refreshTokenExp < new Date());
        } catch (error) {
          console.error('Error decoding refresh token:', error);
        }
      }

      router.push('/app/dashboard');
    }

    // Set random background image safely
    try {
      if (imgUri && imgUri.length > 0) {
        setBackgroundImage(imgUri[Math.floor(Math.random() * imgUri.length)]);
      }
    } catch (err) {
      console.error('Error setting background image:', err);
    }
  }, [isAuthenticated, router, status, user]);

  const toggleTheme = () => {
    setIsDarkMode(!isDarkMode);
    if (!isDarkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  };

  if (status === 'loading') {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>{t.auth.signin.title} | TFLOUU</title>
      </Head>

      <div
        className="relative flex items-center justify-center min-h-screen bg-cover bg-center bg-gray-50 dark:bg-gray-900"
        style={{
          backgroundImage: backgroundImage ? `url('${backgroundImage}')` : 'none',
        }}
      >
        <div className="absolute top-4 right-4 flex items-center gap-4">
          <LanguageSwitcher />
          <button
            className="p-2 bg-white rounded-full shadow-md dark:bg-gray-800"
            onClick={toggleTheme}
            aria-label="Toggle theme"
          >
            {isDarkMode ? '☀️' : '🌙'}
          </button>
        </div>

        <div className="min-h-screen flex flex-col w-full">
          <div className="flex-1 flex items-center justify-center px-4">
            {registered && (
              <div className="absolute top-20 left-0 right-0 mx-auto w-full max-w-md bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400 p-4 rounded-lg text-center">
                {t.auth.signin.registrationSuccess}
              </div>
            )}

            <LoginForm />
          </div>
        </div>
      </div>
    </>
  );
}

const imgUri = [
  'https://images.unsplash.com/photo-1558591710-4b4a1ae0f04d?ixlib=rb-1.2.1&ixid=MXwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHw%3D&auto=format&fit=crop&w=800&q=80',
  'https://images.unsplash.com/photo-1550895030-823330fc2551?ixlib=rb-1.2.1&ixid=MXwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHw%3D&auto=format&fit=crop&w=800&q=80',
  'https://images.unsplash.com/photo-1566041510394-cf7c8fe21800?ixlib=rb-1.2.1&ixid=MXwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHw%3D&auto=format&fit=crop&w=800&q=80',
  'https://images.unsplash.com/photo-1599503815079-dfb7085fc667?ixlib=rb-1.2.1&ixid=MXwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHw%3D&auto=format&fit=crop&w=800&q=80',
  'https://images.unsplash.com/photo-1550989460-0adf9ea622e2?ixlib=rb-1.2.1&ixid=MXwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHw%3D&auto=format&fit=crop&w=800&q=80',
  'https://images.unsplash.com/photo-1578916171728-46686eac8d58?ixlib=rb-1.2.1&ixid=MXwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHw%3D&auto=format&fit=crop&w=800&q=80',
  'https://images.unsplash.com/photo-1598357850706-0188bc0372b2?ixlib=rb-1.2.1&ixid=MXwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHw%3D&auto=format&fit=crop&w=800&q=80',
  'https://images.unsplash.com/photo-1494891848038-7bd202a2afeb?ixlib=rb-1.2.1&ixid=MXwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHw%3D&auto=format&fit=crop&w=800&q=80',
  'https://images.unsplash.com/photo-1584968153986-3f5fe523b044?ixlib=rb-1.2.1&ixid=MXwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHw%3D&auto=format&fit=crop&w=800&q=80',
  'https://images.unsplash.com/photo-1585854467604-cf2080ccef31?ixlib=rb-1.2.1&ixid=MXwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHw%3D&auto=format&fit=crop&w=800&q=80',
  'https://images.unsplash.com/photo-1490365728022-deae76380607?ixlib=rb-1.2.1&ixid=MXwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHw%3D&auto=format&fit=crop&w=800&q=80',
  'https://images.unsplash.com/photo-1501492751416-51484c0d911f?ixlib=rb-1.2.1&ixid=MXwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHw%3D&auto=format&fit=crop&w=1024&q=80',
];
